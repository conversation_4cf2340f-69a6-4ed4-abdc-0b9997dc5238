<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pelatihan - Platform UMKM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    @stack('styles')
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-md">
        <nav class="container px-4 py-4 mx-auto">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-blue-600">
                    Platform UMKM
                </div>
                <div class="hidden space-x-6 md:flex">
                    <a href="/beranda" class="text-gray-600 hover:text-blue-600">Beranda</a>
                    <a href="/profil" class="text-gray-600 hover:text-blue-600">Profil</a>
                    <a href="/berita" class="text-gray-600 hover:text-blue-600">Berita</a>
                    <a href="/pelatihan" class="font-semibold text-blue-600">Pelatihan</a>
                    <a href="/galeri" class="text-gray-600 hover:text-blue-600">Galeri</a>
                    <a href="/peta" class="text-gray-600 hover:text-blue-600">Peta UMKM</a>
                    <a href="/kontak" class="text-gray-600 hover:text-blue-600">Kontak</a>
                </div>
                <div class="flex space-x-2">
                    <a href="/login" class="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700">Masuk</a>
                    <a href="/registrasi" class="px-4 py-2 text-blue-600 border border-blue-600 rounded hover:bg-blue-50">Daftar</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="py-16 text-white bg-blue-600">
        <div class="container px-4 mx-auto text-center">
            <h1 class="mb-4 text-4xl font-bold">Pelatihan & Workshop UMKM</h1>
            <p class="max-w-2xl mx-auto text-xl">
                Tingkatkan keterampilan bisnis Anda melalui berbagai pelatihan dan workshop khusus untuk UMKM
            </p>
        </div>
    </section>

    <!-- Filter & Search -->
    <section class="py-8 bg-white">
        <div class="container px-4 mx-auto">
            <form method="GET" action="{{ route('landingpage.pelatihan') }}" class="flex flex-col items-center justify-between gap-4 md:flex-row">
                <div class="flex gap-4">
                    <select name="kategori" class="px-4 py-2 border border-gray-300 rounded">
                        <option value="">Semua Kategori</option>
                        <option value="Digital Marketing" {{ request('kategori') == 'Digital Marketing' ? 'selected' : '' }}>Digital Marketing</option>
                        <option value="Keuangan" {{ request('kategori') == 'Keuangan' ? 'selected' : '' }}>Keuangan</option>
                        <option value="Produk" {{ request('kategori') == 'Produk' ? 'selected' : '' }}>Produk</option>
                        <option value="Teknologi" {{ request('kategori') == 'Teknologi' ? 'selected' : '' }}>Teknologi</option>
                    </select>
                    <select name="status" class="px-4 py-2 border border-gray-300 rounded">
                        <option value="">Semua Status</option>
                        <option value="open" {{ request('status') == 'open' ? 'selected' : '' }}>Dibuka</option>
                        <option value="upcoming" {{ request('status') == 'upcoming' ? 'selected' : '' }}>Akan Datang</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                    </select>
                </div>
                <div class="relative">
                    <input type="text" name="search" placeholder="Cari pelatihan..." 
                           class="w-64 px-4 py-2 pr-10 border border-gray-300 rounded"
                           value="{{ request('search') }}">
                    <button type="submit" class="absolute right-3 top-2.5">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Featured Training -->
    <section class="py-12">
        <div class="container px-4 mx-auto">
            <h2 class="mb-8 text-3xl font-bold">Pelatihan Unggulan</h2>
            <div class="grid gap-8 mb-12 md:grid-cols-2">
                @foreach($featuredTrainings as $pelatihan)
                <div class="overflow-hidden bg-white rounded-lg shadow-md">
                    @if($pelatihan->gambar)
                        <img src="{{ asset('storage/' . $pelatihan->gambar) }}" class="object-cover w-full h-64" alt="{{ $pelatihan->nama_pelatihan }}">
                    @else
                        <div class="flex items-center justify-center h-64 bg-gray-300">
                            <i class="text-4xl text-gray-500 bi bi-mortarboard"></i>
                        </div>
                    @endif
                    <div class="p-6">
                        <span class="px-3 py-1 text-sm text-blue-800 bg-blue-100 rounded-full">{{ $pelatihan->kategori }}</span>
                        <h3 class="mt-3 mb-2 text-xl font-bold">{{ $pelatihan->nama_pelatihan }}</h3>
                        <p class="mb-4 text-gray-600">{{ Str::limit(strip_tags($pelatihan->deskripsi), 100) }}</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500"><span class="font-semibold">Tanggal:</span> {{ $pelatihan->tanggal_mulai->format('d M Y') }}</p>
                                <p class="text-sm text-gray-500"><span class="font-semibold">Kuota:</span> {{ $pelatihan->peserta_terdaftar }}/{{ $pelatihan->kuota }} peserta</p>
                            </div>
                            <a href="{{ route('landingpage.pelatihan.detail', $pelatihan->id) }}" class="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700">Detail</a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Latest Training -->
    <section class="py-12 bg-white">
        <div class="container px-4 mx-auto">
            <h2 class="mb-8 text-3xl font-bold">Daftar Pelatihan</h2>
            
            @if($pelatihans->count() > 0)
            <div class="grid gap-6 md:grid-cols-3">
                @foreach($pelatihans as $pelatihan)
                <article class="overflow-hidden rounded-lg shadow-md bg-gray-50">
                    @if($pelatihan->gambar)
                        <img src="{{ asset('storage/' . $pelatihan->gambar) }}" class="object-cover w-full h-48" alt="{{ $pelatihan->nama_pelatihan }}">
                    @else
                        <div class="flex items-center justify-center h-48 bg-gray-300">
                            <i class="text-4xl text-gray-500 bi bi-mortarboard"></i>
                        </div>
                    @endif
                    <div class="p-4">
                        <span class="px-2 py-1 text-sm text-purple-800 bg-purple-100 rounded">{{ $pelatihan->kategori }}</span>
                        <h3 class="mt-2 mb-2 font-bold">{{ $pelatihan->nama_pelatihan }}</h3>
                        <p class="mb-3 text-sm text-gray-600">{{ Str::limit(strip_tags($pelatihan->deskripsi), 80) }}</p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>{{ $pelatihan->tanggal_mulai->format('d M Y') }}</span>
                            <span>{{ $pelatihan->peserta_terdaftar }}/{{ $pelatihan->kuota }} peserta</span>
                        </div>
                        <a href="{{ route('landingpage.pelatihan.detail', $pelatihan->id) }}" class="block px-3 py-2 mt-3 text-sm text-center text-white bg-blue-600 rounded hover:bg-blue-700">Detail</a>
                    </div>
                </article>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center mt-12">
                {{ $pelatihans->appends(request()->query())->links() }}
            </div>
            @else
            <div class="py-8 text-center">
                <i class="mb-4 text-4xl text-gray-400 bi bi-inbox"></i>
                <h3 class="text-xl font-semibold">Belum Ada Pelatihan</h3>
                <p class="mt-2 text-gray-600">Saat ini belum ada pelatihan yang tersedia.</p>
                <a href="{{ route('landingpage.pelatihan') }}" class="inline-block px-4 py-2 mt-4 text-white bg-blue-600 rounded hover:bg-blue-700">
                    Lihat Semua Pelatihan
                </a>
            </div>
            @endif
        </div>
    </section>

    <!-- Newsletter -->
    <section class="py-16 text-white bg-blue-600">
        <div class="container px-4 mx-auto text-center">
            <h2 class="mb-4 text-3xl font-bold">Dapatkan Info Pelatihan Terbaru</h2>
            <p class="mb-8 text-xl">Daftarkan email Anda untuk mendapatkan notifikasi pelatihan terbaru</p>
            <form class="flex max-w-md mx-auto">
                <input type="email" placeholder="Masukkan email Anda" class="flex-1 px-4 py-3 text-gray-900 rounded-l">
                <button type="submit" class="px-6 py-3 font-semibold text-blue-600 bg-white rounded-r hover:bg-gray-100">
                    Berlangganan
                </button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 text-white bg-gray-800">
        <div class="container px-4 mx-auto">
            <div class="grid gap-8 md:grid-cols-4">
                <div>
                    <h3 class="mb-4 text-xl font-bold">Platform UMKM</h3>
                    <p class="text-gray-400">Membantu UMKM Indonesia berkembang dengan teknologi dan pelatihan terbaik</p>
                </div>
                <div>
                    <h4 class="mb-4 font-semibold">Menu Utama</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/beranda" class="hover:text-white">Beranda</a></li>
                        <li><a href="/profil" class="hover:text-white">Profil</a></li>
                        <li><a href="/berita" class="hover:text-white">Berita</a></li>
                        <li><a href="/pelatihan" class="hover:text-white">Pelatihan</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="mb-4 font-semibold">Layanan</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/galeri" class="hover:text-white">Galeri</a></li>
                        <li><a href="/peta" class="hover:text-white">Peta UMKM</a></li>
                        <li><a href="/kontak" class="hover:text-white">Kontak</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="mb-4 font-semibold">Kontak</h4>
                    <div class="space-y-2 text-gray-400">
                        <p>Email: <EMAIL></p>
                        <p>Telepon: (021) 1234-5678</p>
                        <p>Alamat: Jakarta, Indonesia</p>
                    </div>
                </div>
            </div>
            <div class="pt-8 mt-8 text-center text-gray-400 border-t border-gray-700">
                <p>&copy; 2024 Platform UMKM. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    @stack('scripts')
</body>
</html>