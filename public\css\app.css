/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

.btn-outline {
    border: 1px solid #2563eb;
    color: #2563eb;
    background-color: transparent;
}

.btn-outline:hover {
    background-color: #eff6ff;
}

.btn-success {
    background-color: #16a34a;
    color: white;
    border-color: #16a34a;
}

.btn-success:hover {
    background-color: #15803d;
    border-color: #15803d;
}

/* ===== LAYOUT ===== */
.header {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2563eb;
}

.nav-link {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-link.active {
    color: #2563eb;
    font-weight: 600;
}

.hero-section {
    background: linear-gradient(to right, #2563eb, #1e40af);
    color: white;
    padding: 5rem 0;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.section {
    padding: 4rem 0;
}

.section-title {
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 3rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
}

/* ===== CARDS ===== */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

/* ===== STATS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.stat-icon {
    padding: 0.5rem;
    border-radius: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.stat-icon.blue {
    background-color: #dbeafe;
    color: #2563eb;
}

.stat-icon.green {
    background-color: #dcfce7;
    color: #16a34a;
}

.stat-icon.yellow {
    background-color: #fef3c7;
    color: #d97706;
}

.stat-icon.purple {
    background-color: #e9d5ff;
    color: #9333ea;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

/* ===== FOOTER ===== */
.footer {
    background-color: #1f2937;
    color: white;
    padding: 3rem 0;
}

.footer-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.footer-subtitle {
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-text {
    color: #9ca3af;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: white;
}

.footer-contact {
    color: #9ca3af;
}

.footer-contact p {
    margin-bottom: 0.5rem;
}

.footer-bottom {
    border-top: 1px solid #374151;
    margin-top: 2rem;
    padding-top: 2rem;
    text-align: center;
    color: #9ca3af;
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input, .form-select, .form-textarea {
    margin-top: 0.25rem;
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* ===== RESPONSIVE GRID ===== */
.grid {
    display: grid;
}

.gap-8 {
    gap: 2rem;
}

@media (min-width: 768px) {
    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ===== UTILITIES ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.text-center {
    text-align: center;
}

.bg-white {
    background-color: white;
}

.hidden {
    display: none;
}

@media (min-width: 768px) {
    .md\:flex {
        display: flex;
    }

    .hidden.md\:flex {
        display: flex;
    }
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.space-x-6 > * + * {
    margin-left: 1.5rem;
}

.flex-col {
    flex-direction: column;
}

.gap-4 {
    gap: 1rem;
}

@media (min-width: 640px) {
    .sm\:flex-row {
        flex-direction: row;
    }
}

/* ===== ADDITIONAL UTILITIES ===== */
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mr-4 {
    margin-right: 1rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.text-sm {
    font-size: 0.875rem;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-gray-600 {
    color: #4b5563;
}

.text-yellow-400 {
    color: #fbbf24;
}

.bg-blue-500 {
    background-color: #3b82f6;
}

.bg-green-500 {
    background-color: #10b981;
}

.bg-purple-500 {
    background-color: #8b5cf6;
}

.rounded-full {
    border-radius: 9999px;
}

.w-8 {
    width: 2rem;
}

.h-8 {
    height: 2rem;
}

.w-12 {
    width: 3rem;
}

.h-12 {
    height: 3rem;
}

.opacity-90 {
    opacity: 0.9;
}

.border-white {
    border-color: white;
}

.text-white {
    color: white;
}

.hover\:bg-white:hover {
    background-color: white;
}

.hover\:text-blue-600:hover {
    color: #2563eb;
}

/* ===== GRADIENT BACKGROUNDS ===== */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-600 {
    --tw-gradient-from: #2563eb;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}

.to-blue-800 {
    --tw-gradient-to: #1e40af;
}

/* ===== AUTH PAGES ===== */
.min-h-screen {
    min-height: 100vh;
}

.max-w-md {
    max-width: 28rem;
}

.w-full {
    width: 100%;
}

.space-y-8 > * + * {
    margin-top: 2rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-t-md {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.rounded-b-md {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.-space-y-px > * + * {
    margin-top: -1px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.text-gray-900 {
    color: #111827;
}

.text-extrabold {
    font-weight: 800;
}

.font-medium {
    font-weight: 500;
}

.ml-2 {
    margin-left: 0.5rem;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-300 {
    border-color: #d1d5db;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.form-checkbox {
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    border-color: #d1d5db;
    border-radius: 0.25rem;
}

.form-checkbox:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== REGISTRATION PAGE ===== */
.max-w-4xl {
    max-width: 56rem;
}

.form-section {
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.step-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.75rem;
}

.step-indicator.active {
    background-color: #2563eb;
    color: white;
}

.step-indicator.inactive {
    background-color: #e5e7eb;
    color: #6b7280;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #2563eb;
    color: #1e40af;
}

.alert-warning {
    background-color: #fef3c7;
    border-color: #d97706;
    color: #92400e;
}

.text-yellow-800 {
    color: #92400e;
}

.text-blue-800 {
    color: #1e40af;
}

.text-gray-700 {
    color: #374151;
}

.text-blue-600 {
    color: #2563eb;
}

.underline {
    text-decoration: underline;
}

.max-w-md {
    max-width: 28rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.text-xs {
    font-size: 0.75rem;
}

.items-start {
    align-items: flex-start;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.badge-success {
    background-color: #dcfce7;
    color: #166534;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fecaca;
    color: #991b1b;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-secondary {
    background-color: #f3f4f6;
    color: #374151;
}

/* ===== ADDITIONAL COLORS ===== */
.bg-yellow-500 {
    background-color: #eab308;
}

.bg-red-500 {
    background-color: #ef4444;
}

.bg-indigo-500 {
    background-color: #6366f1;
}

.bg-pink-500 {
    background-color: #ec4899;
}

.bg-gray-500 {
    background-color: #6b7280;
}

.bg-blue-50 {
    background-color: #eff6ff;
}

.hover\:bg-gray-50:hover {
    background-color: #f9fafb;
}

.max-h-96 {
    max-height: 24rem;
}

.overflow-y-auto {
    overflow-y: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.h-96 {
    height: 24rem;
}

.w-16 {
    width: 4rem;
}

.h-16 {
    height: 4rem;
}

.w-4 {
    width: 1rem;
}

.h-4 {
    height: 1rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.text-xs {
    font-size: 0.75rem;
}

.text-gray-500 {
    color: #6b7280;
}

.text-gray-400 {
    color: #9ca3af;
}

.border-b {
    border-bottom-width: 1px;
}

.border-gray-200 {
    border-color: #e5e7eb;
}

.p-0 {
    padding: 0;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.rounded-b-lg {
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* ===== ENHANCED DASHBOARD ===== */
.sidebar.admin {
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
    width: 280px;
}

.sidebar-logo {
    width: 2rem;
    height: 2rem;
    background-color: #3b82f6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-subtitle {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.25rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #d1d5db;
    text-decoration: none;
    border-radius: 0.5rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s;
}

.sidebar-link:hover {
    background-color: #374151;
    color: white;
}

.sidebar-link.active {
    background-color: #3b82f6;
    color: white;
}

.sidebar-user {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #374151;
}

.user-avatar-small {
    width: 2rem;
    height: 2rem;
    background-color: #3b82f6;
    border-radius: 50%;
}

.user-name-small {
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
}

.user-role {
    font-size: 0.75rem;
    color: #9ca3af;
}

.user-role-header {
    font-size: 0.75rem;
    color: #6b7280;
}

.breadcrumb-container {
    background-color: white;
    border-bottom: 1px solid #e5e7eb;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #6b7280;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* ===== ENHANCED TABLES ===== */
.table-container {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.table-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0.5rem 0 0 0;
}

.table-actions {
    margin-left: auto;
}

.table-filters {
    padding: 1rem 1.5rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.table-wrapper {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    padding: 0.75rem 1.5rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.table td {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    color: #1f2937;
    border-bottom: 1px solid #f3f4f6;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table-pagination {
    padding: 1rem 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: between;
    align-items: center;
}

/* ===== TABLE ACTIONS ===== */
.table-actions-cell {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-table {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s;
}

.btn-table-view {
    background-color: #dbeafe;
    color: #1e40af;
}

.btn-table-view:hover {
    background-color: #bfdbfe;
}

.btn-table-edit {
    background-color: #fef3c7;
    color: #92400e;
}

.btn-table-edit:hover {
    background-color: #fde68a;
}

.btn-table-delete {
    background-color: #fecaca;
    color: #991b1b;
}

.btn-table-delete:hover {
    background-color: #fca5a5;
}

/* ===== STATUS BADGES ===== */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-inactive {
    background-color: #fecaca;
    color: #991b1b;
}

.status-draft {
    background-color: #f3f4f6;
    color: #374151;
}

/* ===== ADDITIONAL UTILITIES ===== */
.w-3 {
    width: 0.75rem;
}

.h-3 {
    height: 0.75rem;
}

.w-5 {
    width: 1.25rem;
}

.h-5 {
    height: 1.25rem;
}

.w-6 {
    width: 1.5rem;
}

.h-6 {
    height: 1.5rem;
}

.ml-3 {
    margin-left: 0.75rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.justify-between {
    justify-content: space-between;
}

.text-red-600 {
    color: #dc2626;
}

.text-green-600 {
    color: #16a34a;
}

.text-yellow-600 {
    color: #ca8a04;
}

.text-purple-600 {
    color: #9333ea;
}

.text-indigo-600 {
    color: #4f46e5;
}

.text-pink-600 {
    color: #db2777;
}

.lg\:col-span-2 {
    grid-column: span 2;
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .lg\:grid-cols-8 {
        grid-template-columns: repeat(8, 1fr);
    }
}

/* ===== DATATABLES CUSTOM STYLES ===== */
.dataTables_wrapper {
    font-family: inherit;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: #374151;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_filter input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dt-buttons {
    margin-bottom: 1rem;
}

.dt-button {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* DataTables responsive */
.dtr-details {
    background-color: #f9fafb;
}

.dtr-details li {
    border-bottom: 1px solid #e5e7eb;
    padding: 0.5rem 0;
}

/* Custom table styles for DataTables */
.table.dataTable {
    border-collapse: separate;
    border-spacing: 0;
}

.table.dataTable thead th {
    border-bottom: 2px solid #e5e7eb;
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.table.dataTable tbody tr:hover {
    background-color: #f9fafb;
}

.table.dataTable tbody tr.selected {
    background-color: #eff6ff;
}

/* Loading overlay */
.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* ===== USER AVATAR ===== */
.user-avatar-small {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.user-avatar-small.bg-blue-500 {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.user-avatar-small.bg-green-500 {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.user-avatar-small.bg-purple-500 {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.user-avatar-small.bg-pink-500 {
    background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.user-avatar-small.bg-yellow-500 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* ===== ADMIN LAYOUT IMPROVEMENTS ===== */
.main-wrapper {
    flex: 1;
    min-height: 100vh;
    background-color: #f9fafb;
}

.dashboard-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.dashboard-header .flex {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.dashboard-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

.breadcrumb-container {
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.breadcrumb-container > div {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Dashboard Cards Improvements */
.dashboard-content .grid {
    margin-left: auto;
    margin-right: auto;
}

.dashboard-content .bg-white {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.15s ease-in-out;
}

.dashboard-content .bg-white:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Better spacing for stats cards */
.dashboard-content .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
    gap: 1.5rem;
}

.dashboard-content .grid.grid-cols-1.lg\:grid-cols-2 {
    gap: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-content {
        padding: 1rem;
    }

    .dashboard-header .flex {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .breadcrumb-container > div {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .dashboard-content .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
        gap: 1rem;
    }

    .dashboard-content .grid.grid-cols-1.lg\:grid-cols-2 {
        gap: 1rem;
    }
}

@media (min-width: 768px) {
    .md\:text-4xl {
        font-size: 2.25rem;
    }

    .md\:text-6xl {
        font-size: 3.75rem;
    }

    .md\:text-2xl {
        font-size: 1.5rem;
    }
}
