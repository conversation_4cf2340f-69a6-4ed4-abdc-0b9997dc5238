<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data UMKM PLUT Purworejo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .badge {
            display: inline-block;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            color: white;
        }
        .badge-primary { background-color: #007bff; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-info { background-color: #17a2b8; }
        .badge-danger { background-color: #dc3545; }
        .badge-success { background-color: #28a745; }
        .badge-secondary { background-color: #6c757d; }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DATA UMKM TERDAFTAR</h1>
        <p>Pusat Layanan Usaha Terpadu (PLUT) Kabupaten Purworejo</p>
        <p>Dicetak pada: {{ now()->format('d F Y, H:i:s') }}</p>
    </div>

    <div class="info-box">
        <strong>Ringkasan Data:</strong><br>
        Total UMKM Terdaftar: {{ $umkms->count() }} UMKM<br>
        Periode: {{ $umkms->min('created_at')?->format('d M Y') ?? '-' }} s/d {{ $umkms->max('created_at')?->format('d M Y') ?? '-' }}
    </div>

    <table class="table">
        <thead>
            <tr>
                <th style="width: 30px;">No</th>
                <th style="width: 150px;">Pemilik</th>
                <th style="width: 120px;">Kontak</th>
                <th style="width: 150px;">UMKM</th>
                <th style="width: 80px;">Bidang</th>
                <th style="width: 150px;">Alamat Usaha</th>
                <th style="width: 100px;">Legalitas</th>
                <th style="width: 70px;">Tgl Daftar</th>
            </tr>
        </thead>
        <tbody>
            @forelse($umkms as $index => $umkm)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>
                    <strong>{{ $umkm->nama }}</strong><br>
                    <small>NIK: {{ optional($umkm->profil)->nik ?? '-' }}</small><br>
                    <small>{{ optional($umkm->profil)->jenis_kelamin ?? '-' }}</small><br>
                    <small style="font-size: 9px;">
                        @if($umkm->profil)
                            {{ $umkm->profil->alamat_lengkap }}, {{ $umkm->profil->desa }}, {{ $umkm->profil->kecamatan }}
                        @else
                            Alamat tidak tersedia
                        @endif
                    </small>
                </td>
                <td>
                    <small>{{ $umkm->no_hp ?? '-' }}</small><br>
                    <small>{{ $umkm->email }}</small>
                </td>
                <td>
                    <strong>{{ optional($umkm->usaha)->nama_usaha ?? 'Belum diisi' }}</strong><br>
                    <small>Merk: {{ optional($umkm->usaha)->nama_merk ?? 'Tidak ada merk' }}</small><br>
                    <small style="font-size: 9px;">
                        Media Sosial: {{ optional($umkm->usaha)->media_sosial ?? 'Tidak ada' }}
                    </small>
                </td>
                <td>
                    @if(optional($umkm->usaha)->bidang_usaha)
                        @php
                            $badgeClass = 'badge-secondary';
                            $bidangUsaha = optional($umkm->usaha)->bidang_usaha;
                            switch($bidangUsaha) {
                                case 'makanan_minuman':
                                    $badgeClass = 'badge-primary';
                                    $displayText = 'Makanan';
                                    break;
                                case 'kerajinan_tangan':
                                    $badgeClass = 'badge-warning';
                                    $displayText = 'Kerajinan';
                                    break;
                                case 'perdagangan':
                                    $badgeClass = 'badge-info';
                                    $displayText = 'Dagang';
                                    break;
                                case 'jasa':
                                    $badgeClass = 'badge-danger';
                                    $displayText = 'Jasa';
                                    break;
                                default:
                                    $displayText = ucfirst(str_replace('_', ' ', $bidangUsaha));
                            }
                        @endphp
                        <span class="badge {{ $badgeClass }}">{{ $displayText }}</span>
                    @else
                        <span class="badge badge-secondary">-</span>
                    @endif
                </td>
                <td>
                    <small>
                        {{ optional($umkm->usaha)->alamat_lengkap_usaha ?? 'Belum diisi' }}<br>
                        {{ optional($umkm->usaha)->desa_usaha ?? '' }}, {{ optional($umkm->usaha)->kecamatan_usaha ?? '' }}
                    </small>
                </td>
                <td>
                    @if($umkm->legalitas && $umkm->legalitas->count() > 0)
                        @foreach($umkm->legalitas as $legal)
                            <small>{{ $legal->nama_legalitas }}: {{ $legal->nomor_legalitas }}</small>
                            @if(!$loop->last)<br>@endif
                        @endforeach
                    @else
                        <small>Belum Ada</small>
                    @endif
                </td>
                <td>
                    <small>{{ $umkm->created_at->format('d/m/Y') }}</small>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="8" style="text-align: center; padding: 20px;">
                    Belum ada data UMKM yang terdaftar
                </td>
            </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>
            Dokumen ini digenerate secara otomatis oleh Sistem PLUT Purworejo<br>
            © {{ date('Y') }} Pusat Layanan Usaha Terpadu Kabupaten Purworejo
        </p>
    </div>

    <script>
        // Auto print when opened
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
