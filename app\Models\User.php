<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'nama',
        'email',
        'password',
        'no_hp',
        'role',
        'status',
        'last_login_at',
        'verification_status',
        'verification_notes',
        'verified_at',
        'verified_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'verified_at' => 'datetime',
        ];
    }

    // Relationships
    public function profil()
    {
        return $this->hasOne(Profil::class);
    }

    public function usaha()
    {
        return $this->hasOne(Usaha::class);
    }

    public function legalitas()
    {
        return $this->hasMany(Legalitas::class);
    }

    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function pelatihans()
    {
        return $this->belongsToMany(Pelatihan::class, 'pelatihan_user')
                    ->withPivot([
                        'status',
                        'tanggal_daftar',
                        'tanggal_konfirmasi',
                        'catatan',
                        'sertifikat_diterima',
                        'nilai'
                    ])
                    ->withTimestamps();
    }

    public function beritas()
    {
        return $this->hasMany(Berita::class, 'author_id');
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        if ($this->profil) {
            return $this->profil->alamat_lengkap . ', ' .
                   $this->profil->desa . ', ' .
                   $this->profil->kecamatan . ', ' .
                   $this->profil->kabupaten . ', ' .
                   $this->profil->provinsi;
        }
        return '';
    }

    public function getBusinessAddressAttribute()
    {
        if ($this->usaha) {
            return $this->usaha->alamat_lengkap_usaha . ', ' .
                   $this->usaha->desa_usaha . ', ' .
                   $this->usaha->kecamatan_usaha . ', ' .
                   $this->usaha->kabupaten_usaha . ', ' .
                   $this->usaha->provinsi_usaha;
        }
        return '';
    }

    public function getLegalitasListAttribute()
    {
        return $this->legalitas->map(function ($legal) {
            return $legal->nama_legalitas . '(' . $legal->nomor_legalitas . ')';
        })->implode(', ');
    }
}
