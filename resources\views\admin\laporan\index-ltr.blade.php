@extends('layouts.admin-ltr')

@section('title', 'Laporan')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/apexcharts-bundle/css/apexcharts.css') }}" rel="stylesheet" />
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Laporan</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download"></i> Export Laporan
            </button>
            <div class="dropdown-menu dropdown-menu-end">
                <a class="dropdown-item" href="#" onclick="exportPDF()"><i class="bi bi-file-pdf"></i> Export PDF</a>
                <a class="dropdown-item" href="#" onclick="exportExcel()"><i class="bi bi-file-excel"></i> Export Excel</a>
                <a class="dropdown-item" href="#" onclick="exportCSV()"><i class="bi bi-file-csv"></i> Export CSV</a>
            </div>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Filter Section -->
<div class="card radius-10 mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-lg-3">
                <label class="form-label">Periode</label>
                <select class="form-select" id="filterPeriode">
                    <option value="bulan_ini" selected>Bulan Ini</option>
                    <option value="3_bulan">3 Bulan Terakhir</option>
                    <option value="6_bulan">6 Bulan Terakhir</option>
                    <option value="tahun_ini">Tahun Ini</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Dari Tanggal</label>
                <input type="date" class="form-control" id="startDate" value="2024-01-01">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Sampai Tanggal</label>
                <input type="date" class="form-control" id="endDate" value="2024-07-31">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="updateReport()">
                        <i class="bi bi-search"></i> Update Laporan
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-primary">
                        <p>Total UMKM Terdaftar</p>
                        <h4 class="text-primary">1,547</h4>
                    </div>
                    <div class="w-50 bg-primary p-3">
                        <p class="mb-3 text-white">+ 156 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-success">
                        <p>UMKM Terverifikasi</p>
                        <h4 class="text-success">1,234</h4>
                    </div>
                    <div class="w-50 bg-success p-3">
                        <p class="mb-3 text-white">+ 89 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-warning">
                        <p>Pelatihan Dilaksanakan</p>
                        <h4 class="text-warning">24</h4>
                    </div>
                    <div class="w-50 bg-warning p-3">
                        <p class="mb-3 text-white">+ 6 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-info">
                        <p>Total Pengguna</p>
                        <h4 class="text-info">2,847</h4>
                    </div>
                    <div class="w-50 bg-info p-3">
                        <p class="mb-3 text-white">+ 234 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end row-->

<!-- Charts Section -->
<div class="row mt-4">
    <div class="col-12 col-lg-8">
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0">Tren Pendaftaran UMKM</h5>
                    <div class="ms-auto">
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary active" onclick="changeChartPeriod('monthly')">Bulanan</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeChartPeriod('weekly')">Mingguan</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeChartPeriod('daily')">Harian</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="registrationChart"></div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4">
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Kategori UMKM</h5>
            </div>
            <div class="card-body">
                <div id="categoryChart"></div>
            </div>
        </div>
    </div>
</div>

<!-- Regional Distribution -->
<div class="row mt-4">
    <div class="col-12 col-lg-6">
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Distribusi per Kecamatan</h5>
            </div>
            <div class="card-body">
                <div id="regionalChart"></div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-6">
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Status Verifikasi</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-success text-success">
                                <i class="bi bi-check-circle-fill"></i>
                            </div>
                            <h4 class="mb-1">1,234</h4>
                            <p class="mb-0 text-muted">Terverifikasi</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-warning text-warning">
                                <i class="bi bi-clock-fill"></i>
                            </div>
                            <h4 class="mb-1">89</h4>
                            <p class="mb-0 text-muted">Menunggu</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-danger text-danger">
                                <i class="bi bi-x-circle-fill"></i>
                            </div>
                            <h4 class="mb-1">24</h4>
                            <p class="mb-0 text-muted">Ditolak</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-info text-info">
                                <i class="bi bi-pause-circle-fill"></i>
                            </div>
                            <h4 class="mb-1">200</h4>
                            <p class="mb-0 text-muted">Draft</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Aktivitas Terbaru</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Waktu</th>
                                <th>Aktivitas</th>
                                <th>User</th>
                                <th>Detail</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2 menit yang lalu</td>
                                <td><span class="badge bg-light-primary text-primary">UMKM Baru</span></td>
                                <td>Warung Makan Sederhana</td>
                                <td>Pendaftaran UMKM baru</td>
                            </tr>
                            <tr>
                                <td>15 menit yang lalu</td>
                                <td><span class="badge bg-light-success text-success">Verifikasi</span></td>
                                <td>Admin PLUT</td>
                                <td>Memverifikasi 3 UMKM</td>
                            </tr>
                            <tr>
                                <td>1 jam yang lalu</td>
                                <td><span class="badge bg-light-warning text-warning">Pelatihan</span></td>
                                <td>Admin PLUT</td>
                                <td>Membuat pelatihan Digital Marketing</td>
                            </tr>
                            <tr>
                                <td>2 jam yang lalu</td>
                                <td><span class="badge bg-light-info text-info">Update</span></td>
                                <td>Toko Kelontong Makmur</td>
                                <td>Update profil usaha</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>

<script>
$(document).ready(function() {
    initializeCharts();
});

function initializeCharts() {
    // Mini sparkline charts
    var sparklineOptions = {
        series: [{
            name: 'Data',
            data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
            height: 50,
            type: 'area',
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth'
        },
        fill: {
            opacity: 0.3,
        },
        colors: ['#ffffff'],
        tooltip: {
            enabled: false
        }
    };
    
    new ApexCharts(document.querySelector("#chart1"), sparklineOptions).render();
    new ApexCharts(document.querySelector("#chart2"), sparklineOptions).render();
    new ApexCharts(document.querySelector("#chart3"), sparklineOptions).render();
    new ApexCharts(document.querySelector("#chart4"), sparklineOptions).render();

    // Registration trend chart
    var registrationOptions = {
        series: [{
            name: 'Pendaftaran UMKM',
            data: [44, 55, 57, 56, 61, 58, 63, 60, 66, 67, 65, 68]
        }],
        chart: {
            type: 'area',
            height: 350
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth'
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'],
        },
        colors: ['#0d6efd']
    };
    new ApexCharts(document.querySelector("#registrationChart"), registrationOptions).render();

    // Category pie chart
    var categoryOptions = {
        series: [44, 25, 15, 10, 6],
        chart: {
            type: 'donut',
            height: 300
        },
        labels: ['Kuliner', 'Fashion', 'Kerajinan', 'Teknologi', 'Lainnya'],
        colors: ['#0d6efd', '#198754', '#ffc107', '#dc3545', '#6f42c1']
    };
    new ApexCharts(document.querySelector("#categoryChart"), categoryOptions).render();

    // Regional bar chart
    var regionalOptions = {
        series: [{
            name: 'Jumlah UMKM',
            data: [400, 300, 250, 200, 180, 150, 120, 100, 80, 60]
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        xaxis: {
            categories: ['Purworejo', 'Bagelen', 'Kutoarjo', 'Grabag', 'Ngombol', 'Pituruh', 'Kemiri', 'Bruno', 'Gebang', 'Bayan'],
        },
        colors: ['#0d6efd']
    };
    new ApexCharts(document.querySelector("#regionalChart"), regionalOptions).render();
}

function updateReport() {
    const periode = document.getElementById('filterPeriode').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    console.log('Updating report:', { periode, startDate, endDate });
    // Here you would make an AJAX call to update the charts with new data
}

function changeChartPeriod(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    console.log('Changing chart period to:', period);
    // Here you would update the registration chart with new data
}

function exportPDF() {
    console.log('Exporting to PDF');
    // Implement PDF export
}

function exportExcel() {
    console.log('Exporting to Excel');
    // Implement Excel export
}

function exportCSV() {
    console.log('Exporting to CSV');
    // Implement CSV export
}
</script>
@endpush
