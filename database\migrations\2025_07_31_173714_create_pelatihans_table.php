<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pelatihans', function (Blueprint $table) {
            $table->id();
            $table->string('nama_pelatihan');
            $table->text('deskripsi');
            $table->string('instruktur');
            $table->string('lokasi');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->time('jam_mulai');
            $table->time('jam_selesai');
            $table->integer('kuota');
            $table->integer('peserta_terdaftar')->default(0);
            $table->decimal('biaya', 10, 2)->default(0);
            $table->string('kategori')->nullable();
            $table->enum('status', ['draft', 'open', 'closed', 'completed', 'cancelled'])->default('draft');
            $table->text('syarat_peserta')->nullable();
            $table->text('materi')->nullable();
            $table->string('sertifikat')->nullable();
            $table->string('gambar')->nullable();
            $table->string('link_online')->nullable(); // Link untuk pelatihan online
            $table->enum('tipe', ['offline', 'online', 'hybrid'])->default('offline');
            $table->integer('notifikasi_terkirim')->default(0); // Jumlah notifikasi terkirim
            $table->timestamp('notifikasi_dikirim_at')->nullable(); // Waktu notifikasi dikirim
            $table->boolean('email_terkirim')->default(false); // Status email terkirim
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pelatihans');
    }
};
