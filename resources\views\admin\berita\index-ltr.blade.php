@extends('layouts.admin-ltr')

@section('title', 'Kelola Berita')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet" />
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Ke<PERSON>la Be<PERSON></li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeritaModal">
                <i class="bi bi-plus-circle"></i> Tambah Berita
            </button>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-end">
                <a class="dropdown-item" href="#"><i class="bi bi-download"></i> Export Excel</a>
                <a class="dropdown-item" href="#"><i class="bi bi-file-pdf"></i> Export PDF</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#"><i class="bi bi-upload"></i> Import Data</a>
            </div>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-primary">
                        <p>Total Berita</p>
                        <h4 class="text-primary">156</h4>
                    </div>
                    <div class="w-50 bg-primary p-3">
                        <p class="mb-3 text-white">+ 12 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-success">
                        <p>Berita Published</p>
                        <h4 class="text-success">134</h4>
                    </div>
                    <div class="w-50 bg-success p-3">
                        <p class="mb-3 text-white">+ 8 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-warning">
                        <p>Draft</p>
                        <h4 class="text-warning">22</h4>
                    </div>
                    <div class="w-50 bg-warning p-3">
                        <p class="mb-3 text-white">+ 4 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-info">
                        <p>Views Bulan Ini</p>
                        <h4 class="text-info">12.5K</h4>
                    </div>
                    <div class="w-50 bg-info p-3">
                        <p class="mb-3 text-white">+ 15% <i class="bi bi-arrow-up"></i></p>
                        <div id="chart4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end row-->

<!-- Filter Section -->
<div class="card radius-10 mt-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-lg-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="filterStatus">
                    <option value="">Semua Status</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                    <option value="archived">Archived</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Kategori</label>
                <select class="form-select" id="filterKategori">
                    <option value="">Semua Kategori</option>
                    <option value="umkm">UMKM</option>
                    <option value="pelatihan">Pelatihan</option>
                    <option value="pengumuman">Pengumuman</option>
                    <option value="event">Event</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Tanggal</label>
                <input type="date" class="form-control" id="filterDate">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Cari</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Judul berita..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Berita Table -->
<div class="card radius-10 mt-4">
    <div class="card-header bg-transparent">
        <div class="row g-3 align-items-center">
            <div class="col">
                <h5 class="mb-0">Daftar Berita</h5>
            </div>
            <div class="col-auto">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-success btn-sm" onclick="publishSelected()">
                        <i class="bi bi-check-circle"></i> Publish Terpilih
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="draftSelected()">
                        <i class="bi bi-file-earmark"></i> Draft Terpilih
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteSelected()">
                        <i class="bi bi-trash"></i> Hapus Terpilih
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="beritaTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>Berita</th>
                        <th>Kategori</th>
                        <th>Status</th>
                        <th>Views</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="1">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <img src="{{ asset('ltr/assets/images/gallery/01.png') }}" alt="" class="rounded" width="50" height="50">
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">Pelatihan Digital Marketing untuk UMKM</h6>
                                    <p class="mb-0 product-category text-secondary">Pelatihan gratis untuk meningkatkan penjualan online...</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-primary text-primary">Pelatihan</span></td>
                        <td><span class="badge bg-light-success text-success">Published</span></td>
                        <td>1,234</td>
                        <td>25 Jul 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/berita/1" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/berita/1/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deleteBerita(1)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="2">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <img src="{{ asset('ltr/assets/images/gallery/02.png') }}" alt="" class="rounded" width="50" height="50">
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">UMKM Purworejo Raih Penghargaan Nasional</h6>
                                    <p class="mb-0 product-category text-secondary">Warung Makan Sederhana meraih juara 1 kategori kuliner...</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-info text-info">UMKM</span></td>
                        <td><span class="badge bg-light-warning text-warning">Draft</span></td>
                        <td>567</td>
                        <td>24 Jul 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/berita/2" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/berita/2/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deleteBerita(2)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="3">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <img src="{{ asset('ltr/assets/images/gallery/03.png') }}" alt="" class="rounded" width="50" height="50">
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">Pembukaan Pendaftaran UMKM Batch 2</h6>
                                    <p class="mb-0 product-category text-secondary">PLUT Purworejo membuka pendaftaran untuk batch kedua...</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-success text-success">Pengumuman</span></td>
                        <td><span class="badge bg-light-success text-success">Published</span></td>
                        <td>2,156</td>
                        <td>23 Jul 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/berita/3" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/berita/3/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deleteBerita(3)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#beritaTable').DataTable({
        "pageLength": 10,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-checkbox').prop('checked', this.checked);
    });

    // Mini charts
    var options1 = {
        series: [{
            name: 'Berita',
            data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
            height: 50,
            type: 'area',
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth'
        },
        fill: {
            opacity: 0.3,
        },
        colors: ['#ffffff'],
        tooltip: {
            enabled: false
        }
    };
    
    var chart1 = new ApexCharts(document.querySelector("#chart1"), options1);
    chart1.render();
    var chart2 = new ApexCharts(document.querySelector("#chart2"), options1);
    chart2.render();
    var chart3 = new ApexCharts(document.querySelector("#chart3"), options1);
    chart3.render();
    var chart4 = new ApexCharts(document.querySelector("#chart4"), options1);
    chart4.render();
});

function publishSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih berita yang akan dipublish');
        return;
    }
    
    if (confirm('Publish ' + selected.length + ' berita yang dipilih?')) {
        console.log('Publishing:', selected);
    }
}

function draftSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih berita yang akan dijadikan draft');
        return;
    }
    
    if (confirm('Jadikan draft ' + selected.length + ' berita yang dipilih?')) {
        console.log('Drafting:', selected);
    }
}

function deleteSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih berita yang akan dihapus');
        return;
    }
    
    if (confirm('Hapus ' + selected.length + ' berita yang dipilih? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting:', selected);
    }
}

function deleteBerita(id) {
    if (confirm('Hapus berita ini? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting berita:', id);
    }
}
</script>
@endpush
