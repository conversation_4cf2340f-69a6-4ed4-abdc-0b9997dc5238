# Integrasi WilayahController dengan Form Registrasi

## Overview

Form registrasi telah diintegrasikan dengan `WilayahController.php` yang sudah ada di <PERSON> project. Controller ini menyediakan API endpoints untuk mengambil data wilayah Indonesia.

## WilayahController Structure

### Controller Location
```
app/Http/Controllers/WilayahController.php
```

### Methods Available
1. `getProvinces()` - Mengambil data provinsi
2. `getRegencies($provinceCode)` - Mengambil data kabupaten/kota berdasarkan kode provinsi
3. `getDistricts($regencyCode)` - Mengambil data kecamatan berdasarkan kode kabupaten
4. `getVillages($districtCode)` - Mengambil data desa/kelurahan berdasarkan kode kecamatan

## API Routes

Routes telah ditambahkan di `routes/web.php`:

```php
// API Routes untuk Wilayah
Route::prefix('api/wilayah')->group(function () {
    Route::get('/provinces', [WilayahController::class, 'getProvinces']);
    Route::get('/regencies/{provinceCode}', [WilayahController::class, 'getRegencies']);
    Route::get('/districts/{regencyCode}', [WilayahController::class, 'getDistricts']);
    Route::get('/villages/{districtCode}', [WilayahController::class, 'getVillages']);
});
```

## API Endpoints

### 1. Get Provinces
```
GET /api/wilayah/provinces
```

**Response Format:**
```json
{
  "data": [
    {
      "kode_wilayah": "11",
      "nama_wilayah": "ACEH"
    },
    {
      "kode_wilayah": "33", 
      "nama_wilayah": "JAWA TENGAH"
    }
  ]
}
```

### 2. Get Regencies
```
GET /api/wilayah/regencies/{provinceCode}
```

**Example:** `/api/wilayah/regencies/33`

**Response Format:**
```json
{
  "data": [
    {
      "kode_wilayah": "33.01",
      "nama_wilayah": "KAB. CILACAP"
    },
    {
      "kode_wilayah": "33.06",
      "nama_wilayah": "KAB. PURWOREJO"
    }
  ]
}
```

### 3. Get Districts
```
GET /api/wilayah/districts/{regencyCode}
```

**Example:** `/api/wilayah/districts/33.06`

**Response Format:**
```json
{
  "data": [
    {
      "kode_wilayah": "33.06.01",
      "nama_wilayah": "BAGELEN"
    },
    {
      "kode_wilayah": "33.06.02",
      "nama_wilayah": "BANYUURIP"
    }
  ]
}
```

### 4. Get Villages
```
GET /api/wilayah/villages/{districtCode}
```

**Example:** `/api/wilayah/villages/33.06.01`

**Response Format:**
```json
{
  "data": [
    {
      "kode_wilayah": "33.06.01.2001",
      "nama_wilayah": "BAGELEN"
    },
    {
      "kode_wilayah": "33.06.01.2002", 
      "nama_wilayah": "BENER"
    }
  ]
}
```

## JavaScript Implementation

### API Configuration
```javascript
const WILAYAH_API = {
    base: '/api/wilayah',
    provinces: '/api/wilayah/provinces',
    regencies: '/api/wilayah/regencies',
    districts: '/api/wilayah/districts',
    villages: '/api/wilayah/villages'
};
```

### Fetch Function
```javascript
async function fetchWilayahData(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Network response was not ok');
        const result = await response.json();
        
        // Handle Laravel WilayahController response format
        if (result.data) {
            return result.data;
        }
        
        return [];
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}
```

### Load Functions
```javascript
// Load provinces
async function loadProvinces() {
    const data = await fetchWilayahData(WILAYAH_API.provinces);
    
    const select = document.getElementById('provinsi_usaha');
    select.innerHTML = '<option value="">Pilih Provinsi</option>';
    
    data.forEach(province => {
        const option = document.createElement('option');
        option.value = province.kode_wilayah;
        option.textContent = province.nama_wilayah;
        select.appendChild(option);
    });
}

// Load regencies
async function loadRegencies(provinceCode) {
    const data = await fetchWilayahData(`${WILAYAH_API.regencies}/${provinceCode}`);
    
    const select = document.getElementById('kabupaten_usaha');
    select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
    
    data.forEach(regency => {
        const option = document.createElement('option');
        option.value = regency.kode_wilayah;
        option.textContent = regency.nama_wilayah;
        select.appendChild(option);
    });
}
```

## Form Implementation

### Alamat Pribadi (Bebas Seluruh Indonesia)
- **Provinsi**: Dropdown dari `/api/wilayah/provinces`
- **Kabupaten**: Dropdown dari `/api/wilayah/regencies/{provinceCode}`
- **Kecamatan**: Dropdown dari `/api/wilayah/districts/{regencyCode}`
- **Desa**: Dropdown dari `/api/wilayah/villages/{districtCode}`

### Alamat Usaha (Lock ke Purworejo)
- **Provinsi**: Lock ke "Jawa Tengah" (readonly)
- **Kabupaten**: Lock ke "Purworejo" (readonly)
- **Kecamatan**: Dropdown dinamis dari `/api/wilayah/districts/33.06`
- **Desa**: Dropdown dinamis berdasarkan kecamatan yang dipilih

## Testing

### 1. Test Page
Akses halaman test: http://127.0.0.1:8000/test-wilayah

### 2. Form Registrasi
Akses form registrasi: http://127.0.0.1:8000/registrasi

### 3. Manual API Testing
```bash
# Test provinces
curl http://127.0.0.1:8000/api/wilayah/provinces

# Test regencies (Jawa Tengah)
curl http://127.0.0.1:8000/api/wilayah/regencies/33

# Test districts (Purworejo)
curl http://127.0.0.1:8000/api/wilayah/districts/33.06

# Test villages (Bagelen)
curl http://127.0.0.1:8000/api/wilayah/villages/33.06.01
```

## Error Handling

### JavaScript Error Handling
```javascript
async function fetchWilayahData(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Network response was not ok');
        const result = await response.json();
        
        if (result.data) {
            return result.data;
        }
        return [];
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}
```

### Loading States
```javascript
function setLoading(selectElement, isLoading) {
    if (isLoading) {
        selectElement.innerHTML = '<option value="">Memuat...</option>';
        selectElement.disabled = true;
    } else {
        selectElement.disabled = false;
    }
}
```

## Compatibility

JavaScript implementation mendukung dua format response:

1. **Laravel WilayahController format**: `{data: [...]}`
2. **Direct API format**: `{status: "success", data: [...]}`

```javascript
// Handle both formats
option.value = province.kode_wilayah || province.code;
option.textContent = province.nama_wilayah || province.name;
```

## Files Modified

1. `routes/web.php` - Menambahkan API routes
2. `resources/views/landingpage/registrasi.blade.php` - Update JavaScript
3. `resources/views/test-wilayah-controller.blade.php` - Halaman test baru

## Benefits

1. ✅ **Performance**: Menggunakan controller Laravel yang sudah ada
2. ✅ **Consistency**: Response format yang konsisten
3. ✅ **Caching**: Bisa menambahkan caching di controller
4. ✅ **Error Handling**: Better error handling di server side
5. ✅ **Security**: CSRF protection dan validation
6. ✅ **Maintenance**: Easier to maintain dan update

## Next Steps

1. Tambahkan caching di WilayahController untuk performance
2. Implementasikan rate limiting untuk API endpoints
3. Tambahkan validation di controller
4. Implementasikan error logging
5. Tambahkan unit tests untuk controller
