@extends('layouts.admin-ltr')

@section('title', 'Kelola UMKM')

@push('styles')
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .widgets-icons {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            font-size: 24px;
        }

        .bg-light-primary {
            background-color: rgba(78, 115, 223, 0.1);
        }

        .bg-light-success {
            background-color: rgba(28, 200, 138, 0.1);
        }

        .bg-light-warning {
            background-color: rgba(246, 194, 62, 0.1);
        }

        .bg-light-info {
            background-color: rgba(54, 185, 204, 0.1);
        }

        .bg-light-secondary {
            background-color: rgba(108, 117, 125, 0.1);
        }

        .bg-light-danger {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #4e73df !important;
        }

        .text-success {
            color: #1cc88a !important;
        }

        .text-warning {
            color: #f6c23e !important;
        }

        .text-info {
            color: #36b9cc !important;
        }

        .text-secondary {
            color: #6c757d !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .radius-10 {
            border-radius: 10px;
        }

        .fw-bold {
            font-weight: 700;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .avatar-sm {
            width: 40px;
            height: 40px;
        }

        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .rounded {
            border-radius: 0.375rem;
        }

        .card-header {
            padding: 0.75rem 1.25rem;
            margin-bottom: 0;
            border-bottom: 1px solid rgba(0, 0, 0, .125);
        }

        .gap-3 {
            gap: 1rem;
        }

        .g-3>* {
            margin-bottom: 1rem;
        }

        .me-2 {
            margin-right: 0.5rem;
        }

        .small {
            font-size: 0.875em;
        }
    </style>
@endpush

@section('content')

    <!--breadcrumb-->
    <div class="mb-3 page-breadcrumb d-none d-sm-flex align-items-center">
        <div class="breadcrumb-title pe-3">UMKM</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="p-0 mb-0 breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Kelola UMKM</li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="showAddModal()">
                    <i class="bi bi-plus-circle"></i> Tambah UMKM
                </button>
                <div class="btn-group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-download"></i> Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('admin.umkm.export') }}">
                            <i class="bi bi-file-earmark-text"></i> Export CSV
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('admin.umkm.export.excel') }}">
                            <i class="bi bi-file-earmark-excel"></i> Export Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('admin.umkm.export.pdf') }}">
                            <i class="bi bi-file-earmark-pdf"></i> Export PDF
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!--end breadcrumb-->





    <!-- Stats Cards -->
    <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
        <div class="col">
            <div class="overflow-hidden card radius-10">
                <div class="p-2 card-body">
                    <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                        <div class="p-3 w-50 bg-light-primary">
                            <p>Total UMKM</p>
                            <h4 class="text-primary">{{ number_format($totalUmkm ?? 0) }}</h4>
                        </div>
                        <div class="p-3 w-50 bg-primary">
                            <p class="mb-3 text-white">
                                @php
                                    $growthPercentage = 0;
                                    if ($umkmThisMonth > 0 && $totalUmkm > 0) {
                                        $growthPercentage = round(($umkmThisMonth / $totalUmkm) * 100, 1);
                                    }
                                @endphp
                                + {{ $growthPercentage }}% <i class="bi bi-arrow-up"></i>
                            </p>
                            <div id="chart1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="overflow-hidden card radius-10">
                <div class="p-2 card-body">
                    <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                        <div class="p-3 w-50 bg-light-success">
                            <p>UMKM Terverifikasi</p>
                            <h4 class="text-success">{{ number_format($umkmVerified ?? 0) }}</h4>
                        </div>
                        <div class="p-3 w-50 bg-success">
                            <p class="mb-3 text-white">
                                {{ $totalUmkm > 0 ? round(($umkmVerified / $totalUmkm) * 100, 1) : 0 }}% <i class="bi bi-check-circle"></i>
                            </p>
                            <div id="chart2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="overflow-hidden card radius-10">
                <div class="p-2 card-body">
                    <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                        <div class="p-3 w-50 bg-light-warning">
                            <p>UMKM Pending</p>
                            <h4 class="text-warning">{{ number_format($umkmPending ?? 0) }}</h4>
                        </div>
                        <div class="p-3 w-50 bg-warning">
                            <p class="mb-3 text-white">
                                {{ $totalUmkm > 0 ? round(($umkmPending / $totalUmkm) * 100, 1) : 0 }}% <i class="bi bi-clock"></i>
                            </p>
                            <div id="chart3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="overflow-hidden card radius-10">
                <div class="p-2 card-body">
                    <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                        <div class="p-3 w-50 bg-light-info">
                            <p>UMKM Bulan Ini</p>
                            <h4 class="text-info">{{ number_format($umkmThisMonth ?? 0) }}</h4>
                        </div>
                        <div class="p-3 w-50 bg-info">
                            <p class="mb-3 text-white">
                                {{ $totalUmkm > 0 ? round(($umkmThisMonth / $totalUmkm) * 100, 1) : 0 }}% <i class="bi bi-calendar-plus"></i>
                            </p>
                            <div id="chart4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end row-->

    <!-- Filter Section -->
    <div class="mt-4 card radius-10">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-12 col-lg-3">
                    <label class="form-label">Status Verifikasi</label>
                    <select class="form-select" id="filterStatus">
                        <option value="">Semua Status</option>
                        <option value="pending">Menunggu Verifikasi</option>
                        <option value="verified">Terverifikasi</option>
                        <option value="rejected">Ditolak</option>
                    </select>
                </div>
                <div class="col-12 col-lg-3">
                    <label class="form-label">Bidang Usaha</label>
                    <select class="form-select" id="filterBidang">
                        <option value="">Semua Bidang</option>
                        <option value="makanan_minuman">Makanan & Minuman</option>
                        <option value="kerajinan_tangan">Kerajinan Tangan</option>
                        <option value="perdagangan">Perdagangan</option>
                        <option value="jasa">Jasa</option>
                    </select>
                </div>
                <div class="col-12 col-lg-3">
                    <label class="form-label">Kecamatan</label>
                    <select class="form-select" id="filterKecamatan">
                        <option value="">Semua Kecamatan</option>
                        <!-- Options will be populated dynamically -->
                    </select>
                </div>
                <div class="col-12 col-lg-3">
                    <label class="form-label">Cari</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Nama UMKM atau pemilik..."
                            id="searchInput">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- UMKM Table -->
    <div class="mt-4 card radius-10">
        <div class="bg-transparent card-header">
            <div class="row g-3 align-items-center">
                <div class="col">
                    <h5 class="mb-0">Daftar UMKM</h5>
                </div>
                <div class="col-auto">
                    <div class="gap-2 d-flex align-items-center">
                        <button class="btn btn-success btn-sm" onclick="bulkApprove()">
                            <i class="bi bi-check-circle"></i> Setujui Terpilih
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="bulkReject()">
                            <i class="bi bi-x-circle"></i> Tolak Terpilih
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="bi bi-x"></i> Batal Pilihan
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="umkmTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>UMKM</th>
                            <th>Pemilik</th>
                            <th>Kontak</th>
                            <th>Alamat</th>
                            <th>Bidang Usaha</th>
                            <th>Legalitas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="umkmTableBody">
                        @forelse($umkms as $umkm)
                            <tr data-status="{{ $umkm->verification_status ?? 'pending' }}"
                                data-bidang="{{ $umkm->usaha->bidang_usaha ?? '' }}"
                                data-kecamatan="{{ $umkm->usaha->kecamatan_usaha ?? ($umkm->profil->kecamatan ?? '') }}">
                                <td>
                                    <input type="checkbox" class="form-check-input row-checkbox"
                                        value="{{ $umkm->id }}"
                                        data-status="{{ $umkm->verification_status ?? 'pending' }}">
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $umkm->usaha->nama_usaha ?? 'Belum diisi' }}</div>
                                        <small
                                            class="text-muted">{{ Str::limit($umkm->usaha->deskripsi ?? 'Tidak ada deskripsi', 50) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="gap-2 d-flex align-items-center">
                                        <div>
                                            @if($umkm->profil && $umkm->profil->foto_profil)
                                                <img src="{{ asset('storage/' . $umkm->profil->foto_profil) }}" alt="Foto Profil" class="rounded-circle" width="35" height="35" style="object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                    <i class="bi bi-person text-muted"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $umkm->nama }}</div>
                                            <small class="text-muted">NIK: {{ $umkm->profil->nik ?? 'Belum diisi' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><i class="bi bi-envelope text-primary"></i> {{ $umkm->email }}</div>
                                        <div><i class="bi bi-phone text-success"></i> {{ $umkm->no_hp ?? 'Belum diisi' }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div>
                                            {{ Str::limit($umkm->usaha->alamat_lengkap_usaha ?? ($umkm->profil->alamat_lengkap ?? 'Belum diisi'), 40) }}
                                        </div>
                                        <small
                                            class="text-muted">{{ $umkm->usaha->kecamatan_usaha ?? ($umkm->profil->kecamatan ?? 'Belum diisi') }}</small>
                                    </div>
                                </td>
                                <td>
                                    @php
                                        $bidangColors = [
                                            'makanan_minuman' => 'info',
                                            'kerajinan_tangan' => 'success',
                                            'perdagangan' => 'warning',
                                            'jasa' => 'danger',
                                        ];
                                        $bidangColor = $bidangColors[$umkm->usaha->bidang_usaha ?? ''] ?? 'secondary';
                                    @endphp
                                    <span
                                        class="badge bg-{{ $bidangColor }}">{{ ucwords(str_replace('_', ' & ', $umkm->usaha->bidang_usaha ?? 'Belum diisi')) }}</span>
                                </td>
                                <td>
                                    <div>
                                        @if ($umkm->legalitas && $umkm->legalitas->count() > 0)
                                            @foreach ($umkm->legalitas->take(2) as $legal)
                                                <span
                                                    class="mb-1 badge bg-light-primary text-primary me-1">{{ $legal->nama_legalitas }}</span>
                                            @endforeach
                                            @if ($umkm->legalitas->count() > 2)
                                                <small class="text-muted">+{{ $umkm->legalitas->count() - 2 }}
                                                    lainnya</small>
                                            @endif
                                        @else
                                            <span class="badge bg-light-secondary text-secondary">Belum ada</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'verified' => 'success',
                                            'pending' => 'warning',
                                            'rejected' => 'danger',
                                        ];
                                        $statusColor =
                                            $statusColors[$umkm->verification_status ?? 'pending'] ?? 'secondary';
                                    @endphp
                                    <span
                                        class="badge bg-{{ $statusColor }}">{{ ucfirst($umkm->verification_status ?? 'pending') }}</span>
                                </td>
                                <td>
                                    <div class="gap-2 d-flex">
                                        <button class="btn btn-sm btn-outline-primary"
                                            onclick="showDetail({{ $umkm->id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning"
                                            onclick="showEditModal({{ $umkm->id }})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteUmkm({{ $umkm->id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="py-4 text-center text-muted">
                                    Belum ada data UMKM yang terdaftar
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>



    <!-- Modal Detail UMKM -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">Detail UMKM</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-primary" id="exportUserBtn">
                        <i class="bi bi-download"></i> Export Data
                    </button>
                    <button type="button" class="btn btn-warning" id="editFromDetailBtn">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <button type="button" class="btn btn-danger" id="deleteFromDetailBtn">
                        <i class="bi bi-trash"></i> Hapus
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Add/Edit UMKM -->
    <div class="modal fade" id="addEditModal" tabindex="-1" aria-labelledby="addEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEditModalLabel">Tambah UMKM</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="umkmForm">
                        @csrf
                        <input type="hidden" id="umkm_id" name="umkm_id">

                        <!-- Personal Information -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <h6 class="text-primary">Informasi Pribadi</h6>
                                <hr>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="nama" class="form-label">Nama Lengkap <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama" name="nama" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="no_hp" class="form-label">No. HP <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="no_hp" name="no_hp" required>
                            </div>
                            <div class="col-md-6">
                                <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nik" name="nik" maxlength="16"
                                    required>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password <span class="text-danger"
                                        id="password-required">*</span></label>
                                <input type="password" class="form-control" id="password" name="password">
                                <small class="text-muted" id="password-help">Kosongkan jika tidak ingin mengubah
                                    password</small>
                            </div>
                            <div class="col-md-6">
                                <label for="foto_profil" class="form-label">Foto Profil</label>
                                <input type="file" class="form-control" id="foto_profil" name="foto_profil" accept="image/*">
                                <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                                <div id="foto_preview" class="mt-2" style="display: none;">
                                    <img id="preview_image" src="" alt="Preview" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="jenis_kelamin" class="form-label">Jenis Kelamin <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="jenis_kelamin" name="jenis_kelamin" required>
                                    <option value="">Pilih Jenis Kelamin</option>
                                    <option value="Laki-laki">Laki-laki</option>
                                    <option value="Perempuan">Perempuan</option>
                                </select>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <h6 class="text-primary">Alamat Pribadi</h6>
                                <hr>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label for="provinsi" class="form-label">Provinsi <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="provinsi" name="provinsi" required>
                                    <option value="">Pilih Provinsi</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="kabupaten" class="form-label">Kabupaten <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="kabupaten" name="kabupaten" required>
                                    <option value="">Pilih Kabupaten</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="kecamatan" class="form-label">Kecamatan <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="kecamatan" name="kecamatan" required>
                                    <option value="">Pilih Kecamatan</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="desa" class="form-label">Desa <span class="text-danger">*</span></label>
                                <select class="form-select" id="desa" name="desa" required>
                                    <option value="">Pilih Desa</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-12">
                                <label for="alamat_lengkap" class="form-label">Alamat Lengkap <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="alamat_lengkap" name="alamat_lengkap" rows="3" required></textarea>
                            </div>
                        </div>

                        <!-- Business Information -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <h6 class="text-primary">Informasi Usaha</h6>
                                <hr>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="nama_usaha" class="form-label">Nama Usaha <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama_usaha" name="nama_usaha" required>
                            </div>
                            <div class="col-md-6">
                                <label for="nama_merk" class="form-label">Nama Merk</label>
                                <input type="text" class="form-control" id="nama_merk" name="nama_merk">
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label for="bidang_usaha" class="form-label">Bidang Usaha <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="bidang_usaha" name="bidang_usaha" required>
                                    <option value="">Pilih Bidang Usaha</option>
                                    <option value="makanan_minuman">Makanan & Minuman</option>
                                    <option value="kerajinan_tangan">Kerajinan Tangan</option>
                                    <option value="perdagangan">Perdagangan</option>
                                    <option value="jasa">Jasa</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-12">
                                <label for="deskripsi" class="form-label">Deskripsi Usaha</label>
                                <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- Business Address -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <h6 class="text-primary">Alamat Usaha</h6>
                                <hr>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label for="provinsi_usaha" class="form-label">Provinsi</label>
                                <input type="text" class="form-control" id="provinsi_usaha_display" value="Jawa Tengah" readonly>
                                <input type="hidden" id="provinsi_usaha" name="provinsi_usaha" value="33">
                            </div>
                            <div class="col-md-3">
                                <label for="kabupaten_usaha" class="form-label">Kabupaten</label>
                                <input type="text" class="form-control" id="kabupaten_usaha_display" value="Purworejo" readonly>
                                <input type="hidden" id="kabupaten_usaha" name="kabupaten_usaha" value="33.06">
                            </div>
                            <div class="col-md-3">
                                <label for="kecamatan_usaha" class="form-label">Kecamatan <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="kecamatan_usaha" name="kecamatan_usaha" required>
                                    <option value="">Pilih Kecamatan</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="desa_usaha" class="form-label">Desa <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" id="desa_usaha" name="desa_usaha" required>
                                    <option value="">Pilih Desa</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-12">
                                <label for="alamat_lengkap_usaha" class="form-label">Alamat Lengkap Usaha <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="alamat_lengkap_usaha" name="alamat_lengkap_usaha" rows="3" required></textarea>
                            </div>
                        </div>

                        <!-- Legalitas Information -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <h6 class="text-primary">Legalitas & Dokumen</h6>
                                <hr>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-12">
                                <label class="form-label">Jenis Legalitas</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="legalitas_nib"
                                                name="legalitas_types[]" value="NIB">
                                            <label class="form-check-label" for="legalitas_nib">NIB</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="legalitas_siup"
                                                name="legalitas_types[]" value="SIUP">
                                            <label class="form-check-label" for="legalitas_siup">SIUP</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="legalitas_pirt"
                                                name="legalitas_types[]" value="PIRT">
                                            <label class="form-check-label" for="legalitas_pirt">PIRT</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="legalitas_hki"
                                                name="legalitas_types[]" value="HKI">
                                            <label class="form-check-label" for="legalitas_hki">HKI</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- NIB -->
                        <div class="mb-3 row" id="nib_section" style="display: none;">
                            <div class="col-12">
                                <label for="nib_nomor" class="form-label">Nomor NIB</label>
                                <input type="text" class="form-control" id="nib_nomor" name="nib_nomor"
                                    placeholder="Masukkan nomor NIB">
                            </div>
                        </div>

                        <!-- SIUP -->
                        <div class="mb-3 row" id="siup_section" style="display: none;">
                            <div class="col-12">
                                <label for="siup_nomor" class="form-label">Nomor SIUP</label>
                                <input type="text" class="form-control" id="siup_nomor" name="siup_nomor"
                                    placeholder="Masukkan nomor SIUP">
                            </div>
                        </div>

                        <!-- PIRT -->
                        <div class="mb-3 row" id="pirt_section" style="display: none;">
                            <div class="col-12">
                                <label for="pirt_nomor" class="form-label">Nomor PIRT</label>
                                <input type="text" class="form-control" id="pirt_nomor" name="pirt_nomor"
                                    placeholder="Masukkan nomor PIRT">
                            </div>
                        </div>

                        <!-- HKI -->
                        <div class="mb-3 row" id="hki_section" style="display: none;">
                            <div class="col-12">
                                <label for="hki_nomor" class="form-label">Nomor HKI</label>
                                <input type="text" class="form-control" id="hki_nomor" name="hki_nomor"
                                    placeholder="Masukkan nomor HKI">
                            </div>
                        </div>

                        <!-- Other Legalitas -->
                        <div class="mb-3 row">
                            <div class="col-12">
                                <label class="form-label">Legalitas Lainnya</label>
                                <div id="other_legalitas_container">
                                    <!-- Dynamic legalitas fields will be added here -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="addOtherLegalitas()">
                                    <i class="bi bi-plus"></i> Tambah Legalitas Lain
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" onclick="saveUmkm()">Simpan</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- ApexCharts -->
    <script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>

    <script>
        // CSRF Token Setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // DataTable Initialization
        $(document).ready(function() {
            $('#umkmTable').DataTable({
                responsive: true,
                language: {
                    "sProcessing": "Sedang memproses...",
                    "sLengthMenu": "Tampilkan _MENU_ entri",
                    "sZeroRecords": "Tidak ditemukan data yang sesuai",
                    "sInfo": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                    "sInfoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
                    "sInfoFiltered": "(disaring dari _MAX_ entri keseluruhan)",
                    "sInfoPostFix": "",
                    "sSearch": "Cari:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Pertama",
                        "sPrevious": "Sebelumnya",
                        "sNext": "Selanjutnya",
                        "sLast": "Terakhir"
                    }
                },
                pageLength: 10,
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, "Semua"]
                ],
                order: [
                    [1, 'asc']
                ],
                columnDefs: [{
                    orderable: false,
                    targets: [0, 8]
                }]
            });

            // Load provinces on page load (for personal address only)
            loadProvinces();

            // Load Purworejo districts for business address
            loadPurworejoDistricts();

            // Event handlers for personal address
            $('#provinsi').change(function() {
                const provinceCode = $(this).val();
                if (provinceCode) {
                    loadRegencies(provinceCode, '#kabupaten');
                } else {
                    $('#kabupaten, #kecamatan, #desa').empty().append('<option value="">Pilih...</option>');
                }
            });

            $('#kabupaten').change(function() {
                const regencyCode = $(this).val();
                if (regencyCode) {
                    loadDistricts(regencyCode, '#kecamatan');
                } else {
                    $('#kecamatan, #desa').empty().append('<option value="">Pilih...</option>');
                }
            });

            $('#kecamatan').change(function() {
                const districtCode = $(this).val();
                if (districtCode) {
                    loadVillages(districtCode, '#desa');
                } else {
                    $('#desa').empty().append('<option value="">Pilih Desa</option>');
                }
            });

            // Event handler for business address (only kecamatan and desa)
            $('#kecamatan_usaha').change(function() {
                const districtCode = $(this).val();
                if (districtCode) {
                    loadVillages(districtCode, '#desa_usaha');
                } else {
                    $('#desa_usaha').empty().append('<option value="">Pilih Desa</option>');
                }
            });

            // Handle legalitas checkboxes
            $('input[name="legalitas_types[]"]').change(function() {
                const value = $(this).val().toLowerCase();
                const section = $('#' + value + '_section');

                if ($(this).is(':checked')) {
                    section.show();
                } else {
                    section.hide();
                    section.find('input').val('');
                }
            });

            // Handle select all checkbox
            $('#selectAll').change(function() {
                const isChecked = $(this).is(':checked');
                $('.row-checkbox:visible').prop('checked', isChecked);
                updateBulkActions();
            });

            // Handle individual row checkboxes
            $(document).on('change', '.row-checkbox', function() {
                updateBulkActions();

                // Update select all checkbox
                const totalVisible = $('.row-checkbox:visible').length;
                const totalChecked = $('.row-checkbox:visible:checked').length;

                $('#selectAll').prop('indeterminate', totalChecked > 0 && totalChecked < totalVisible);
                $('#selectAll').prop('checked', totalChecked === totalVisible && totalVisible > 0);
            });

            // Populate kecamatan filter
            populateKecamatanFilter();

            // Initialize charts
            initializeCharts();

            // Apply initial filter from URL if exists
            @if(isset($initialFilter))
                $('#filterStatus').val('{{ $initialFilter }}');
                applyFilters();
            @endif

            // Real-time filter event listeners
            $('#filterStatus, #filterBidang, #filterKecamatan').change(function() {
                applyFilters();
            });

            $('#searchInput').on('input', debounce(function() {
                applyFilters();
            }, 500));

            $('#searchBtn').click(function() {
                applyFilters();
            });
        });

        // Bulk Actions Functions
        function updateBulkActions() {
            const checkedBoxes = $('.row-checkbox:checked');
            const count = checkedBoxes.length;

            if (count > 0) {
                $('#selectedCount').text(count);
                $('#bulkActionsCard').show();
            } else {
                $('#bulkActionsCard').hide();
            }
        }

        function clearSelection() {
            $('.row-checkbox').prop('checked', false);
            $('#selectAll').prop('checked', false).prop('indeterminate', false);
            updateBulkActions();
        }

        function bulkApprove() {
            const selectedIds = [];
            $('.row-checkbox:checked').each(function() {
                selectedIds.push($(this).val());
            });

            if (selectedIds.length === 0) {
                Swal.fire('Peringatan', 'Pilih UMKM yang akan disetujui', 'warning');
                return;
            }

            Swal.fire({
                title: `Setujui ${selectedIds.length} UMKM?`,
                text: "UMKM yang dipilih akan disetujui dan statusnya berubah menjadi verified.",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, setujui semua!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/admin/umkm/approve-multiple',
                        method: 'POST',
                        data: {
                            ids: selectedIds
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Error', 'Terjadi kesalahan saat menyetujui UMKM', 'error');
                        }
                    });
                }
            });
        }

        function bulkReject() {
            const selectedIds = [];
            $('.row-checkbox:checked').each(function() {
                selectedIds.push($(this).val());
            });

            if (selectedIds.length === 0) {
                Swal.fire('Peringatan', 'Pilih UMKM yang akan ditolak', 'warning');
                return;
            }

            Swal.fire({
                title: `Tolak ${selectedIds.length} UMKM?`,
                html: `
                <p>UMKM yang dipilih akan ditolak dan statusnya berubah menjadi rejected.</p>
                <div class="mt-3">
                    <label for="bulk-rejection-reason" class="form-label">Alasan Penolakan:</label>
                    <textarea id="bulk-rejection-reason" class="form-control" rows="3" placeholder="Masukkan alasan penolakan..."></textarea>
                </div>
            `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, tolak semua!',
                cancelButtonText: 'Batal',
                preConfirm: () => {
                    const reason = document.getElementById('bulk-rejection-reason').value;
                    if (!reason.trim()) {
                        Swal.showValidationMessage('Alasan penolakan harus diisi');
                        return false;
                    }
                    return reason;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/admin/umkm/reject-multiple',
                        method: 'POST',
                        data: {
                            ids: selectedIds,
                            reason: result.value
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Error', 'Terjadi kesalahan saat menolak UMKM', 'error');
                        }
                    });
                }
            });
        }

        // Filter Functions
        function populateKecamatanFilter() {
            const kecamatanSet = new Set();
            $('tr[data-kecamatan]').each(function() {
                const kecamatan = $(this).data('kecamatan');
                if (kecamatan && kecamatan.trim() !== '') {
                    kecamatanSet.add(kecamatan);
                }
            });

            const kecamatanArray = Array.from(kecamatanSet).sort();
            const select = $('#filterKecamatan');

            kecamatanArray.forEach(function(kecamatan) {
                select.append(`<option value="${kecamatan}">${kecamatan}</option>`);
            });
        }

        function applyFilters() {
            const statusFilter = $('#filterStatus').val();
            const bidangFilter = $('#filterBidang').val();
            const kecamatanFilter = $('#filterKecamatan').val();
            const searchTerm = $('#searchInput').val();

            // Show loading state
            $('#umkmTableBody').html(`
                <tr>
                    <td colspan="9" class="py-4 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 mb-0">Memuat data UMKM...</p>
                    </td>
                </tr>
            `);

            // Make AJAX request
            $.ajax({
                url: '/admin/umkm/filter',
                method: 'POST',
                data: {
                    status: statusFilter,
                    bidang: bidangFilter,
                    kecamatan: kecamatanFilter,
                    search: searchTerm
                },
                success: function(response) {
                    if (response.success) {
                        $('#umkmTableBody').html(response.html);

                        // Update bulk actions
                        updateBulkActions();

                        // Reset select all checkbox
                        $('#selectAll').prop('checked', false).prop('indeterminate', false);

                        // Show result message
                        if (statusFilter || bidangFilter || kecamatanFilter || searchTerm) {
                            Swal.fire({
                                title: 'Filter Diterapkan',
                                text: `Menampilkan ${response.count} UMKM dari hasil filter`,
                                icon: 'info',
                                timer: 2000,
                                showConfirmButton: false
                            });
                        }

                        // Show empty state if no results
                        if (response.count === 0) {
                            $('#umkmTableBody').html(`
                                <tr>
                                    <td colspan="9" class="py-4 text-center text-muted">
                                        Tidak ada UMKM yang sesuai dengan filter yang dipilih
                                    </td>
                                </tr>
                            `);
                        }
                    } else {
                        Swal.fire('Error', response.message || 'Terjadi kesalahan saat memfilter data', 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Terjadi kesalahan saat memfilter data', 'error');
                    // Reload page on error
                    location.reload();
                }
            });
        }

        function resetFilters() {
            $('#filterStatus').val('');
            $('#filterBidang').val('');
            $('#filterKecamatan').val('');
            $('#searchInput').val('');

            // Apply filters with empty values to reload all data
            applyFilters();

            Swal.fire({
                title: 'Filter Direset',
                text: 'Semua filter telah dihapus',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            });
        }

        // Search functionality
        $('#searchBtn').click(function() {
            applyFilters();
        });

        $('#searchInput').keypress(function(e) {
            if (e.which === 13) { // Enter key
                applyFilters();
            }
        });

        // Wilayah API Functions
        function loadProvinces() {
            $.get('/api/wilayah/provinces', function(data) {
                const provinces = data.data || data;
                $('#provinsi').empty().append('<option value="">Pilih Provinsi</option>');
                provinces.forEach(function(province) {
                    const code = province.kode_wilayah || province.code;
                    const name = province.nama_wilayah || province.name;
                    $('#provinsi').append(`<option value="${code}">${name}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load provinces');
            });
        }

        // Load Purworejo districts for business address
        function loadPurworejoDistricts() {
            $.get('/api/wilayah/districts/33.06', function(data) {
                const districts = data.data || data;
                $('#kecamatan_usaha').empty().append('<option value="">Pilih Kecamatan</option>');
                districts.forEach(function(district) {
                    const code = district.kode_wilayah || district.code;
                    const name = district.nama_wilayah || district.name;
                    $('#kecamatan_usaha').append(`<option value="${code}">${name}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load Purworejo districts');
            });
        }

        function loadRegencies(provinceCode, targetSelect, businessAddress = false) {
            if (!provinceCode) {
                $(targetSelect).empty().append('<option value="">Pilih Kabupaten</option>');
                return;
            }

            $.get(`/api/wilayah/regencies/${provinceCode}`, function(data) {
                const regencies = data.data || data;
                $(targetSelect).empty().append('<option value="">Pilih Kabupaten</option>');
                regencies.forEach(function(regency) {
                    const code = regency.kode_wilayah || regency.code;
                    const name = regency.nama_wilayah || regency.name;
                    $(targetSelect).append(`<option value="${code}">${name}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load regencies');
            });
        }

        function loadDistricts(regencyCode, targetSelect) {
            if (!regencyCode) {
                $(targetSelect).empty().append('<option value="">Pilih Kecamatan</option>');
                return;
            }

            $.get(`/api/wilayah/districts/${regencyCode}`, function(data) {
                const districts = data.data || data;
                $(targetSelect).empty().append('<option value="">Pilih Kecamatan</option>');
                districts.forEach(function(district) {
                    const code = district.kode_wilayah || district.code;
                    const name = district.nama_wilayah || district.name;
                    $(targetSelect).append(`<option value="${code}">${name}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load districts');
            });
        }

        function loadVillages(districtCode, targetSelect) {
            if (!districtCode) {
                $(targetSelect).empty().append('<option value="">Pilih Desa</option>');
                return;
            }

            $.get(`/api/wilayah/villages/${districtCode}`, function(data) {
                const villages = data.data || data;
                $(targetSelect).empty().append('<option value="">Pilih Desa</option>');
                villages.forEach(function(village) {
                    const code = village.kode_wilayah || village.code;
                    const name = village.nama_wilayah || village.name;
                    $(targetSelect).append(`<option value="${code}">${name}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load villages');
            });
        }

        // Event Handlers for Address Dropdowns
        $('#provinsi').change(function() {
            const provinceCode = $(this).val();
            loadRegencies(provinceCode, '#kabupaten');
            $('#kecamatan, #desa').empty().append('<option value="">Pilih Kecamatan</option>');
        });

        $('#kabupaten').change(function() {
            const regencyCode = $(this).val();
            loadDistricts(regencyCode, '#kecamatan');
            $('#desa').empty().append('<option value="">Pilih Desa</option>');
        });

        $('#kecamatan').change(function() {
            const districtCode = $(this).val();
            loadVillages(districtCode, '#desa');
        });

        // Business Address Event Handlers
        $('#provinsi_usaha').change(function() {
            const provinceCode = $(this).val();
            loadRegencies(provinceCode, '#kabupaten_usaha');
            $('#kecamatan_usaha, #desa_usaha').empty().append('<option value="">Pilih Kecamatan</option>');
        });

        $('#kabupaten_usaha').change(function() {
            const regencyCode = $(this).val();
            loadDistricts(regencyCode, '#kecamatan_usaha');
            $('#desa_usaha').empty().append('<option value="">Pilih Desa</option>');
        });

        $('#kecamatan_usaha').change(function() {
            const districtCode = $(this).val();
            loadVillages(districtCode, '#desa_usaha');
        });

        // Modal Functions
        // Add other legalitas function
        let otherLegalitasCount = 0;

        function addOtherLegalitas() {
            otherLegalitasCount++;
            const html = `
            <div class="mb-2 row" id="other_legalitas_${otherLegalitasCount}">
                <div class="col-md-5">
                    <input type="text" class="form-control" name="other_legalitas_nama[]" placeholder="Nama Legalitas">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" name="other_legalitas_nomor[]" placeholder="Nomor Legalitas">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOtherLegalitas(${otherLegalitasCount})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
            $('#other_legalitas_container').append(html);
        }

        function removeOtherLegalitas(id) {
            $('#other_legalitas_' + id).remove();
        }

        function showAddModal() {
            $('#addEditModalLabel').text('Tambah UMKM');
            $('#umkmForm')[0].reset();
            $('#umkm_id').val('');
            $('#password').prop('required', true);
            $('#password-required').show();
            $('#password-help').hide();

            // Reset legalitas
            $('input[name="legalitas_types[]"]').prop('checked', false);
            $('.row[id$="_section"]').hide();
            $('#other_legalitas_container').empty();
            otherLegalitasCount = 0;

            // Load provinces for personal address
            loadProvinces();

            // Load Purworejo districts for business address
            loadPurworejoDistricts();

            // Set locked values for business address
            $('#provinsi_usaha').val('33');
            $('#kabupaten_usaha').val('33.06');

            $('#addEditModal').modal('show');
        }

        function showEditModal(id) {
            $('#addEditModalLabel').text('Edit UMKM');
            $('#umkm_id').val(id);
            $('#password').prop('required', false);
            $('#password-required').hide();
            $('#password-help').show();

            // Load UMKM data
            $.get(`/admin/umkm/${id}/edit`, function(response) {
                if (response.success) {
                    const data = response.data;

                    // Populate personal information
                    $('#nama').val(data.nama);
                    $('#email').val(data.email);
                    $('#no_hp').val(data.no_hp);
                    $('#nik').val(data.nik);
                    $('#jenis_kelamin').val(data.jenis_kelamin);
                    $('#alamat_lengkap').val(data.alamat_lengkap);

                    // Populate business information
                    $('#nama_usaha').val(data.nama_usaha);
                    $('#nama_merk').val(data.nama_merk);
                    $('#bidang_usaha').val(data.bidang_usaha);
                    $('#deskripsi').val(data.deskripsi);
                    $('#alamat_lengkap_usaha').val(data.alamat_lengkap_usaha);

                    // Reset and populate legalitas
                    $('input[name="legalitas_types[]"]').prop('checked', false);
                    $('.row[id$="_section"]').hide();
                    $('#other_legalitas_container').empty();
                    otherLegalitasCount = 0;

                    if (data.legalitas && data.legalitas.length > 0) {
                        data.legalitas.forEach(function(legal) {
                            const legalType = legal.nama_legalitas.toLowerCase();
                            const checkbox = $(`input[value="${legal.nama_legalitas}"]`);

                            if (checkbox.length > 0) {
                                checkbox.prop('checked', true);
                                $(`#${legalType}_section`).show();
                                $(`#${legalType}_nomor`).val(legal.nomor_legalitas);
                            } else {
                                // Add to other legalitas
                                addOtherLegalitas();
                                const lastContainer = $('#other_legalitas_container .row:last');
                                lastContainer.find('input[name="other_legalitas_nama[]"]').val(legal
                                    .nama_legalitas);
                                lastContainer.find('input[name="other_legalitas_nomor[]"]').val(legal
                                    .nomor_legalitas);
                            }
                        });
                    }

                    // Load provinces and set selected values
                    loadProvinces();

                    // Set address values after provinces are loaded
                    setTimeout(() => {
                        if (data.provinsi) {
                            $('#provinsi').val(data.provinsi);
                            loadRegencies(data.provinsi, '#kabupaten');

                            setTimeout(() => {
                                if (data.kabupaten) {
                                    $('#kabupaten').val(data.kabupaten);
                                    loadDistricts(data.kabupaten, '#kecamatan');

                                    setTimeout(() => {
                                        if (data.kecamatan) {
                                            $('#kecamatan').val(data.kecamatan);
                                            loadVillages(data.kecamatan, '#desa');

                                            setTimeout(() => {
                                                if (data.desa) {
                                                    $('#desa').val(data.desa);
                                                }
                                            }, 500);
                                        }
                                    }, 500);
                                }
                            }, 500);
                        }

                        // Business address
                        if (data.provinsi_usaha) {
                            $('#provinsi_usaha').val(data.provinsi_usaha);
                            loadRegencies(data.provinsi_usaha, '#kabupaten_usaha');

                            setTimeout(() => {
                                if (data.kabupaten_usaha) {
                                    $('#kabupaten_usaha').val(data.kabupaten_usaha);
                                    loadDistricts(data.kabupaten_usaha, '#kecamatan_usaha');

                                    setTimeout(() => {
                                        if (data.kecamatan_usaha) {
                                            $('#kecamatan_usaha').val(data.kecamatan_usaha);
                                            loadVillages(data.kecamatan_usaha,
                                                '#desa_usaha');

                                            setTimeout(() => {
                                                if (data.desa_usaha) {
                                                    $('#desa_usaha').val(data
                                                        .desa_usaha);
                                                }
                                            }, 500);
                                        }
                                    }, 500);
                                }
                            }, 500);
                        }
                    }, 1000);

                } else {
                    Swal.fire('Error', response.message, 'error');
                }
            }).fail(function() {
                Swal.fire('Error', 'Gagal memuat data UMKM', 'error');
            });

            $('#addEditModal').modal('show');
        }

        let currentUmkmId = null;

        function showDetail(id) {
            currentUmkmId = id;
            $.get(`/admin/umkm/${id}/detail`, function(response) {
                if (response.success) {
                    $('#detailModalBody').html(response.html);

                    // Setup modal action buttons
                    $('#exportUserBtn').off('click').on('click', function() {
                        window.open(`/admin/umkm/${currentUmkmId}/export`, '_blank');
                    });

                    $('#editFromDetailBtn').off('click').on('click', function() {
                        $('#detailModal').modal('hide');
                        setTimeout(() => showEditModal(currentUmkmId), 300);
                    });

                    $('#deleteFromDetailBtn').off('click').on('click', function() {
                        $('#detailModal').modal('hide');
                        setTimeout(() => deleteUmkm(currentUmkmId), 300);
                    });

                    $('#detailModal').modal('show');
                } else {
                    Swal.fire('Error', response.message, 'error');
                }
            }).fail(function() {
                Swal.fire('Error', 'Gagal memuat detail UMKM', 'error');
            });
        }

        function saveUmkm() {
            const formData = new FormData($('#umkmForm')[0]);
            const umkmId = $('#umkm_id').val();
            const url = umkmId ? `/admin/umkm/${umkmId}` : '/admin/umkm';
            const method = umkmId ? 'PUT' : 'POST';

            if (method === 'PUT') {
                formData.append('_method', 'PUT');
            }

            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Berhasil', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('Error', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    const errors = xhr.responseJSON?.errors;
                    if (errors) {
                        let errorMessage = '';
                        Object.keys(errors).forEach(key => {
                            errorMessage += errors[key][0] + '\n';
                        });
                        Swal.fire('Validation Error', errorMessage, 'error');
                    } else {
                        Swal.fire('Error', 'Terjadi kesalahan saat menyimpan data', 'error');
                    }
                }
            });
        }

        function deleteUmkm(id) {
            Swal.fire({
                title: 'Apakah Anda yakin?',
                text: "Data UMKM akan dihapus permanen!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/umkm/${id}`,
                        method: 'DELETE',
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('Terhapus!', response.message, 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Error', 'Terjadi kesalahan saat menghapus data', 'error');
                        }
                    });
                }
            });
        }

        function approveUmkm(id) {
            Swal.fire({
                title: 'Setujui UMKM?',
                text: "UMKM akan disetujui dan statusnya berubah menjadi verified.",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, setujui!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/umkm/${id}/approve`,
                        method: 'POST',
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                    $('#detailModal').modal('hide');
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Error', 'Terjadi kesalahan saat menyetujui UMKM', 'error');
                        }
                    });
                }
            });
        }

        function rejectUmkm(id) {
            Swal.fire({
                title: 'Tolak UMKM?',
                text: "UMKM akan ditolak dan statusnya berubah menjadi rejected.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, tolak!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/umkm/${id}/reject`,
                        method: 'POST',
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                    $('#detailModal').modal('hide');
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Error', 'Terjadi kesalahan saat menolak UMKM', 'error');
                        }
                    });
                }
            });
        }

        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize Charts
        function initializeCharts() {
            var options = {
                series: [{
                    name: 'UMKM',
                    data: [31, 40, 28, 51, 42, 109, 100]
                }],
                chart: {
                    height: 50,
                    type: 'area',
                    sparkline: {
                        enabled: true
                    }
                },
                stroke: {
                    curve: 'smooth'
                },
                fill: {
                    opacity: 0.3,
                },
                colors: ['#ffffff'],
                tooltip: {
                    enabled: false
                }
            };

            var chart1 = new ApexCharts(document.querySelector("#chart1"), options);
            chart1.render();
            var chart2 = new ApexCharts(document.querySelector("#chart2"), options);
            chart2.render();
            var chart3 = new ApexCharts(document.querySelector("#chart3"), options);
            chart3.render();
            var chart4 = new ApexCharts(document.querySelector("#chart4"), options);
            chart4.render();
        }

        // Photo preview functionality
        $('#foto_profil').change(function() {
            const file = this.files[0];
            if (file) {
                // Validate file size (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    Swal.fire('Error', 'Ukuran file terlalu besar. Maksimal 2MB', 'error');
                    $(this).val('');
                    $('#foto_preview').hide();
                    return;
                }

                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    Swal.fire('Error', 'Format file tidak didukung. Gunakan JPG, PNG, atau GIF', 'error');
                    $(this).val('');
                    $('#foto_preview').hide();
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#preview_image').attr('src', e.target.result);
                    $('#foto_preview').show();
                };
                reader.readAsDataURL(file);
            } else {
                $('#foto_preview').hide();
            }
        });
    </script>
@endpush
