@extends('layouts.admin-ltr')

@section('title', '<PERSON><PERSON><PERSON>')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet" />
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page"><PERSON><PERSON><PERSON></li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPelatihanModal">
                <i class="bi bi-plus-circle"></i> Tambah Pelatihan
            </button>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-end">
                <a class="dropdown-item" href="#"><i class="bi bi-download"></i> Export Excel</a>
                <a class="dropdown-item" href="#"><i class="bi bi-file-pdf"></i> Export PDF</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#"><i class="bi bi-people"></i> Kelola Peserta</a>
            </div>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-primary">
                        <p>Total Pelatihan</p>
                        <h4 class="text-primary">24</h4>
                    </div>
                    <div class="w-50 bg-primary p-3">
                        <p class="mb-3 text-white">+ 3 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-success">
                        <p>Sedang Berlangsung</p>
                        <h4 class="text-success">8</h4>
                    </div>
                    <div class="w-50 bg-success p-3">
                        <p class="mb-3 text-white">+ 2 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-warning">
                        <p>Akan Datang</p>
                        <h4 class="text-warning">6</h4>
                    </div>
                    <div class="w-50 bg-warning p-3">
                        <p class="mb-3 text-white">+ 1 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-info">
                        <p>Total Peserta</p>
                        <h4 class="text-info">456</h4>
                    </div>
                    <div class="w-50 bg-info p-3">
                        <p class="mb-3 text-white">+ 45 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end row-->

<!-- Filter Section -->
<div class="card radius-10 mt-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-lg-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="filterStatus">
                    <option value="">Semua Status</option>
                    <option value="upcoming">Akan Datang</option>
                    <option value="ongoing">Sedang Berlangsung</option>
                    <option value="completed">Selesai</option>
                    <option value="cancelled">Dibatalkan</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Kategori</label>
                <select class="form-select" id="filterKategori">
                    <option value="">Semua Kategori</option>
                    <option value="digital_marketing">Digital Marketing</option>
                    <option value="keuangan">Keuangan</option>
                    <option value="produksi">Produksi</option>
                    <option value="manajemen">Manajemen</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Tanggal</label>
                <input type="date" class="form-control" id="filterDate">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Cari</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Nama pelatihan..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pelatihan Table -->
<div class="card radius-10 mt-4">
    <div class="card-header bg-transparent">
        <div class="row g-3 align-items-center">
            <div class="col">
                <h5 class="mb-0">Daftar Pelatihan</h5>
            </div>
            <div class="col-auto">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-success btn-sm" onclick="startSelected()">
                        <i class="bi bi-play-circle"></i> Mulai Terpilih
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="postponeSelected()">
                        <i class="bi bi-pause-circle"></i> Tunda Terpilih
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="cancelSelected()">
                        <i class="bi bi-x-circle"></i> Batalkan Terpilih
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="pelatihanTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>Pelatihan</th>
                        <th>Kategori</th>
                        <th>Status</th>
                        <th>Peserta</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="1">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="bi bi-laptop"></i>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">Digital Marketing untuk UMKM</h6>
                                    <p class="mb-0 product-category text-secondary">Pelatihan strategi pemasaran digital untuk meningkatkan penjualan</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-primary text-primary">Digital Marketing</span></td>
                        <td><span class="badge bg-light-success text-success">Sedang Berlangsung</span></td>
                        <td>45/50</td>
                        <td>25-27 Jul 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/pelatihan/1" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/1/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/1/peserta" class="text-info" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Kelola Peserta">
                                    <i class="bi bi-people-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deletePelatihan(1)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="2">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <div class="bg-success text-white rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="bi bi-calculator"></i>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">Manajemen Keuangan UMKM</h6>
                                    <p class="mb-0 product-category text-secondary">Pelatihan pengelolaan keuangan dan pembukuan sederhana</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-success text-success">Keuangan</span></td>
                        <td><span class="badge bg-light-warning text-warning">Akan Datang</span></td>
                        <td>32/40</td>
                        <td>01-03 Agu 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/pelatihan/2" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/2/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/2/peserta" class="text-info" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Kelola Peserta">
                                    <i class="bi bi-people-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deletePelatihan(2)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="3">
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    <div class="bg-warning text-white rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">Optimasi Produksi UMKM</h6>
                                    <p class="mb-0 product-category text-secondary">Pelatihan meningkatkan efisiensi dan kualitas produksi</p>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-light-warning text-warning">Produksi</span></td>
                        <td><span class="badge bg-light-secondary text-secondary">Selesai</span></td>
                        <td>38/40</td>
                        <td>15-17 Jul 2024</td>
                        <td>
                            <div class="d-flex align-items-center gap-3 fs-6">
                                <a href="/admin/pelatihan/3" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Detail">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/3/edit" class="text-warning" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a href="/admin/pelatihan/3/peserta" class="text-info" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Kelola Peserta">
                                    <i class="bi bi-people-fill"></i>
                                </a>
                                <a href="javascript:;" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Hapus" onclick="deletePelatihan(3)">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#pelatihanTable').DataTable({
        "pageLength": 10,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-checkbox').prop('checked', this.checked);
    });

    // Mini charts
    var options1 = {
        series: [{
            name: 'Pelatihan',
            data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
            height: 50,
            type: 'area',
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth'
        },
        fill: {
            opacity: 0.3,
        },
        colors: ['#ffffff'],
        tooltip: {
            enabled: false
        }
    };
    
    var chart1 = new ApexCharts(document.querySelector("#chart1"), options1);
    chart1.render();
    var chart2 = new ApexCharts(document.querySelector("#chart2"), options1);
    chart2.render();
    var chart3 = new ApexCharts(document.querySelector("#chart3"), options1);
    chart3.render();
    var chart4 = new ApexCharts(document.querySelector("#chart4"), options1);
    chart4.render();
});

function startSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih pelatihan yang akan dimulai');
        return;
    }
    
    if (confirm('Mulai ' + selected.length + ' pelatihan yang dipilih?')) {
        console.log('Starting:', selected);
    }
}

function postponeSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih pelatihan yang akan ditunda');
        return;
    }
    
    if (confirm('Tunda ' + selected.length + ' pelatihan yang dipilih?')) {
        console.log('Postponing:', selected);
    }
}

function cancelSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih pelatihan yang akan dibatalkan');
        return;
    }
    
    if (confirm('Batalkan ' + selected.length + ' pelatihan yang dipilih?')) {
        console.log('Cancelling:', selected);
    }
}

function deletePelatihan(id) {
    if (confirm('Hapus pelatihan ini? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting pelatihan:', id);
    }
}
</script>
@endpush
