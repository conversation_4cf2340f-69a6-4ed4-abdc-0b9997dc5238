<?php

namespace App\Mail;

use App\Models\Pelatihan;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PelatihanBaruMail extends Mailable
{
    use Queueable, SerializesModels;

    public $pelatihan;
    public $user;

    /**
     * Create a new message instance.
     */
    public function __construct(Pelatihan $pelatihan, User $user)
    {
        $this->pelatihan = $pelatihan;
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Pelatihan Baru: ' . $this->pelatihan->nama_pelatihan,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.pelatihan-baru',
            with: [
                'pelatihan' => $this->pelatihan,
                'user' => $this->user,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
