<tr data-status="{{ $umkm->verification_status ?? 'pending' }}"
    data-bidang="{{ $umkm->usaha->bidang_usaha ?? '' }}"
    data-kecamatan="{{ $umkm->usaha->kecamatan_usaha ?? ($umkm->profil->kecamatan ?? '') }}">
    <td>
        <input type="checkbox" class="form-check-input row-checkbox"
            value="{{ $umkm->id }}"
            data-status="{{ $umkm->verification_status ?? 'pending' }}">
    </td>
    <td>
        <div>
            <div class="fw-bold">{{ $umkm->usaha->nama_usaha ?? 'Belum diisi' }}</div>
            <small
                class="text-muted">{{ Str::limit($umkm->usaha->deskripsi ?? 'Tidak ada deskripsi', 50) }}</small>
        </div>
    </td>
    <td>
        <div class="d-flex align-items-center gap-2">
            <div>
                @if($umkm->profil && $umkm->profil->foto_profil)
                    <img src="{{ asset('storage/' . $umkm->profil->foto_profil) }}" alt="Foto Profil" class="rounded-circle" width="35" height="35" style="object-fit: cover;">
                @else
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                        <i class="bi bi-person text-muted"></i>
                    </div>
                @endif
            </div>
            <div>
                <div class="fw-bold">{{ $umkm->nama }}</div>
                <small class="text-muted">NIK: {{ $umkm->profil->nik ?? 'Belum diisi' }}</small>
            </div>
        </div>
    </td>
    <td>
        <div>
            <div><i class="bi bi-envelope text-primary"></i> {{ $umkm->email }}</div>
            <div><i class="bi bi-phone text-success"></i> {{ $umkm->no_hp ?? 'Belum diisi' }}
            </div>
        </div>
    </td>
    <td>
        <div>
            <div>
                {{ Str::limit($umkm->usaha->alamat_lengkap_usaha ?? ($umkm->profil->alamat_lengkap ?? 'Belum diisi'), 40) }}
            </div>
            <small
                class="text-muted">{{ $umkm->usaha->kecamatan_usaha ?? ($umkm->profil->kecamatan ?? 'Belum diisi') }}</small>
        </div>
    </td>
    <td>
        @php
            $bidangColors = [
                'makanan_minuman' => 'info',
                'kerajinan_tangan' => 'success',
                'perdagangan' => 'warning',
                'jasa' => 'danger',
            ];
            $bidangColor = $bidangColors[$umkm->usaha->bidang_usaha ?? ''] ?? 'secondary';
        @endphp
        <span
            class="badge bg-{{ $bidangColor }}">{{ ucwords(str_replace('_', ' & ', $umkm->usaha->bidang_usaha ?? 'Belum diisi')) }}</span>
    </td>
    <td>
        <div>
            @if ($umkm->legalitas && $umkm->legalitas->count() > 0)
                @foreach ($umkm->legalitas->take(2) as $legal)
                    <span
                        class="badge bg-light-primary text-primary mb-1 me-1">{{ $legal->nama_legalitas }}</span>
                @endforeach
                @if ($umkm->legalitas->count() > 2)
                    <small class="text-muted">+{{ $umkm->legalitas->count() - 2 }}
                        lainnya</small>
                @endif
            @else
                <span class="badge bg-light-secondary text-secondary">Belum ada</span>
            @endif
        </div>
    </td>
    <td>
        @php
            $statusColors = [
                'verified' => 'success',
                'pending' => 'warning',
                'rejected' => 'danger',
            ];
            $statusColor =
                $statusColors[$umkm->verification_status ?? 'pending'] ?? 'secondary';
        @endphp
        <span
            class="badge bg-{{ $statusColor }}">{{ ucfirst($umkm->verification_status ?? 'pending') }}</span>
    </td>
    <td>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary"
                onclick="showDetail({{ $umkm->id }})">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-warning"
                onclick="showEditModal({{ $umkm->id }})">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger"
                onclick="deleteUmkm({{ $umkm->id }})">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    </td>
</tr>
