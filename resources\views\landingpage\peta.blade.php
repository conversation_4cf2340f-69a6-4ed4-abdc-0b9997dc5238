@extends('layouts.landingpage')

@section('title', 'Peta UMKM - PLUT Purworejo')

@section('main-content')
    <!-- Header Section -->
    <section class="section bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Peta UMKM Kabupaten Purworejo
                </h1>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Jelajahi dan temukan lokasi UMKM di seluruh Kabupaten Purworejo. Peta interaktif ini menampilkan sebaran UMKM berdasarkan kecamatan dan jenis usaha.
                </p>
            </div>

            <!-- Filter Section -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold mb-4">Filter UMKM</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="form-group">
                        <label for="filter_kecamatan" class="form-label">Kecamatan</label>
                        <select id="filter_kecamatan" class="form-select">
                            <option value="">Semua Kecamatan</option>
                            <option value="bagelen">Bagelen</option>
                            <option value="banyuurip">Banyuurip</option>
                            <option value="bayan">Bayan</option>
                            <option value="bener">Bener</option>
                            <option value="bruno">Bruno</option>
                            <option value="gebang">Gebang</option>
                            <option value="grabag">Grabag</option>
                            <option value="kaligesing">Kaligesing</option>
                            <option value="kemiri">Kemiri</option>
                            <option value="kutoarjo">Kutoarjo</option>
                            <option value="loano">Loano</option>
                            <option value="ngombol">Ngombol</option>
                            <option value="pituruh">Pituruh</option>
                            <option value="purwodadi">Purwodadi</option>
                            <option value="purworejo">Purworejo</option>
                            <option value="rembang">Rembang</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_bidang" class="form-label">Bidang Usaha</label>
                        <select id="filter_bidang" class="form-select">
                            <option value="">Semua Bidang</option>
                            <option value="makanan_minuman">Makanan & Minuman</option>
                            <option value="fashion_tekstil">Fashion & Tekstil</option>
                            <option value="kerajinan_tangan">Kerajinan Tangan</option>
                            <option value="pertanian_perikanan">Pertanian & Perikanan</option>
                            <option value="jasa_perdagangan">Jasa & Perdagangan</option>
                            <option value="teknologi_digital">Teknologi & Digital</option>
                            <option value="kesehatan_kecantikan">Kesehatan & Kecantikan</option>
                            <option value="otomotif">Otomotif</option>
                            <option value="konstruksi">Konstruksi & Bangunan</option>
                            <option value="pendidikan">Pendidikan & Pelatihan</option>
                            <option value="pariwisata">Pariwisata & Hospitality</option>
                            <option value="lainnya">Lainnya</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_status" class="form-label">Status</label>
                        <select id="filter_status" class="form-select">
                            <option value="">Semua Status</option>
                            <option value="aktif">Aktif</option>
                            <option value="terdaftar">Terdaftar</option>
                            <option value="binaan">Binaan PLUT</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" id="btn_filter" class="btn btn-primary w-full">
                            🔍 Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="section bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Map Container -->
                <div class="lg:col-span-2">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Peta Interaktif UMKM Purworejo</h3>
                        </div>
                        <div class="card-body p-0">
                            <div id="map-container" class="w-full h-96 bg-gray-200 rounded-b-lg relative">
                                <!-- Placeholder Map -->
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="text-center">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <p class="text-gray-600">Peta UMKM Purworejo</p>
                                        <p class="text-sm text-gray-500">Integrasi dengan Google Maps/Leaflet akan ditambahkan</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Map Legend -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">Legenda Peta</h4>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Makanan & Minuman</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Kerajinan Tangan</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Fashion & Tekstil</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Pertanian</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Jasa & Perdagangan</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-indigo-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Teknologi</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-pink-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Pariwisata</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-gray-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Lainnya</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- UMKM List -->
                <div class="lg:col-span-1">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Daftar UMKM</h3>
                            <p class="text-sm text-gray-600">Total: <span id="total-umkm">2,500</span> UMKM</p>
                        </div>
                        <div class="card-body p-0">
                            <div id="umkm-list" class="max-h-96 overflow-y-auto">
                                <!-- Sample UMKM Items -->
                                <div class="border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer umkm-item" data-kecamatan="purworejo" data-bidang="makanan_minuman">
                                    <h4 class="font-semibold text-sm">Warung Makan Bu Sari</h4>
                                    <p class="text-xs text-gray-600">Makanan & Minuman</p>
                                    <p class="text-xs text-gray-500">📍 Kec. Purworejo</p>
                                    <div class="flex items-center mt-2">
                                        <span class="badge badge-success">Aktif</span>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer umkm-item" data-kecamatan="kaligesing" data-bidang="kerajinan_tangan">
                                    <h4 class="font-semibold text-sm">Kerajinan Bambu Pak Budi</h4>
                                    <p class="text-xs text-gray-600">Kerajinan Tangan</p>
                                    <p class="text-xs text-gray-500">📍 Kec. Kaligesing</p>
                                    <div class="flex items-center mt-2">
                                        <span class="badge badge-info">Binaan PLUT</span>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer umkm-item" data-kecamatan="bagelen" data-bidang="jasa_perdagangan">
                                    <h4 class="font-semibold text-sm">Toko Oleh-oleh Maya</h4>
                                    <p class="text-xs text-gray-600">Jasa & Perdagangan</p>
                                    <p class="text-xs text-gray-500">📍 Kec. Bagelen</p>
                                    <div class="flex items-center mt-2">
                                        <span class="badge badge-success">Aktif</span>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer umkm-item" data-kecamatan="kutoarjo" data-bidang="fashion_tekstil">
                                    <h4 class="font-semibold text-sm">Konveksi Sejahtera</h4>
                                    <p class="text-xs text-gray-600">Fashion & Tekstil</p>
                                    <p class="text-xs text-gray-500">📍 Kec. Kutoarjo</p>
                                    <div class="flex items-center mt-2">
                                        <span class="badge badge-warning">Terdaftar</span>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer umkm-item" data-kecamatan="grabag" data-bidang="pertanian_perikanan">
                                    <h4 class="font-semibold text-sm">Kelompok Tani Makmur</h4>
                                    <p class="text-xs text-gray-600">Pertanian & Perikanan</p>
                                    <p class="text-xs text-gray-500">📍 Kec. Grabag</p>
                                    <div class="flex items-center mt-2">
                                        <span class="badge badge-info">Binaan PLUT</span>
                                    </div>
                                </div>

                                <!-- More UMKM items would be loaded dynamically -->
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-outline w-full">
                                Lihat Semua UMKM
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="section bg-white">
        <div class="container mx-auto px-4">
            <h2 class="section-title">Statistik UMKM per Kecamatan</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">156</div>
                    <div class="stat-label text-xs">Purworejo</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">142</div>
                    <div class="stat-label text-xs">Kutoarjo</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">138</div>
                    <div class="stat-label text-xs">Grabag</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">125</div>
                    <div class="stat-label text-xs">Bagelen</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">118</div>
                    <div class="stat-label text-xs">Kaligesing</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">95</div>
                    <div class="stat-label text-xs">Banyuurip</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">89</div>
                    <div class="stat-label text-xs">Kemiri</div>
                </div>
                <div class="stat-card text-center">
                    <div class="stat-value text-lg">82</div>
                    <div class="stat-label text-xs">Ngombol</div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterBtn = document.getElementById('btn_filter');
    const umkmItems = document.querySelectorAll('.umkm-item');
    
    filterBtn.addEventListener('click', function() {
        const kecamatan = document.getElementById('filter_kecamatan').value;
        const bidang = document.getElementById('filter_bidang').value;
        const status = document.getElementById('filter_status').value;
        
        let visibleCount = 0;
        
        umkmItems.forEach(item => {
            let show = true;
            
            if (kecamatan && item.dataset.kecamatan !== kecamatan) {
                show = false;
            }
            
            if (bidang && item.dataset.bidang !== bidang) {
                show = false;
            }
            
            if (show) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });
        
        // Update total count
        document.getElementById('total-umkm').textContent = visibleCount;
    });
    
    // UMKM item click handler
    umkmItems.forEach(item => {
        item.addEventListener('click', function() {
            // Highlight selected item
            umkmItems.forEach(i => i.classList.remove('bg-blue-50'));
            this.classList.add('bg-blue-50');
            
            // Here you would typically center the map on the selected UMKM
            console.log('Selected UMKM:', this.querySelector('h4').textContent);
        });
    });
});
</script>
@endpush
