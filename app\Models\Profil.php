<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profil extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nik',
        'jenis_kelamin',
        'alamat_lengkap',
        'desa',
        'kecamatan',
        'kabupaten',
        'provinsi',
        'foto_profil',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        return $this->alamat_lengkap . ', ' . 
               $this->desa . ', ' . 
               $this->kecamatan . ', ' . 
               $this->kabupaten . ', ' . 
               $this->provinsi;
    }
}
