<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Legalitas extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nama_legalitas',
        'nomor_legalitas',
        'tanggal_terbit',
        'tanggal_berakhir',
        'file_path',
    ];

    protected $casts = [
        'tanggal_terbit' => 'date',
        'tanggal_berakhir' => 'date',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getFormattedLegalitasAttribute()
    {
        return $this->nama_legalitas . '(' . $this->nomor_legalitas . ')';
    }
}
