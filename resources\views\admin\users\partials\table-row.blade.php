<tr data-role="{{ $user->role }}" data-status="{{ $user->status ?? 'aktif' }}">
    <td>
        <div class="d-flex align-items-center gap-3">
            <div class="product-box border">
                @if($user->profil && $user->profil->foto_profil)
                    <img src="{{ asset('storage/' . $user->profil->foto_profil) }}" alt="Foto Profil" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @else
                    <img src="{{ asset('ltr/assets/images/avatars/avatar-' . (($user->id % 10) + 1) . '.png') }}" alt="Avatar Default" class="rounded-circle" width="40" height="40">
                @endif
            </div>
            <div class="product-info">
                <h6 class="product-title mb-1">{{ $user->nama }}</h6>
                <p class="mb-0 product-category text-muted">{{ $user->email }}</p>
                @if($user->no_hp)
                    <small class="text-muted"><i class="bi bi-phone"></i> {{ $user->no_hp }}</small>
                @endif
            </div>
        </div>
    </td>
    <td>
        @if($user->role === 'admin')
            <span class="badge bg-primary">Administrator</span>
        @else
            <span class="badge bg-info">Pemilik UMKM</span>
        @endif
    </td>
    <td>
        @php
            $status = $user->status ?? 'aktif';
            $statusColor = $status === 'aktif' ? 'success' : ($status === 'suspended' ? 'warning' : 'secondary');
        @endphp
        <span class="badge bg-{{ $statusColor }}">{{ ucfirst($status) }}</span>
    </td>
    <td>
        @if($user->last_login_at)
            <span class="text-muted">{{ $user->last_login_at->diffForHumans() }}</span>
        @else
            <span class="text-muted">Belum pernah login</span>
        @endif
    </td>
    <td>
        <span class="text-muted">{{ $user->created_at->format('d M Y') }}</span>
    </td>
    <td>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="showUserInfo({{ $user->id }})" data-bs-toggle="tooltip" title="Lihat Detail">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-warning" onclick="editUser({{ $user->id }})" data-bs-toggle="tooltip" title="Edit">
                <i class="bi bi-pencil"></i>
            </button>
            @if($user->status === 'aktif' || !$user->status)
                <button class="btn btn-sm btn-outline-warning" onclick="suspendUser({{ $user->id }})" data-bs-toggle="tooltip" title="Suspend">
                    <i class="bi bi-pause"></i>
                </button>
            @else
                <button class="btn btn-sm btn-outline-success" onclick="activateUser({{ $user->id }})" data-bs-toggle="tooltip" title="Aktifkan">
                    <i class="bi bi-play"></i>
                </button>
            @endif
            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ $user->id }})" data-bs-toggle="tooltip" title="Hapus">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    </td>
</tr>
