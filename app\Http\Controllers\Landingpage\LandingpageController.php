<?php

namespace App\Http\Controllers\Landingpage;

use App\Http\Controllers\Controller;
use App\Models\Pelatihan;
use Illuminate\Http\Request;

class LandingpageController extends Controller
{
    public function beranda()
    {
        // TODO: Implement beranda logic
        return view('landingpage.beranda');
    }

    public function profil()
    {
        // TODO: Implement profil logic
        return view('landingpage.profil');
    }

    public function berita()
    {
        // TODO: Implement berita logic
        return view('landingpage.berita');
    }

    public function pelatihan(Request $request)
    {
        $query = Pelatihan::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            // Default: show only open and closed pelatihan (not draft)
            $query->whereIn('status', ['open', 'closed', 'completed']);
        }

        // Filter by tipe
        if ($request->filled('tipe')) {
            $query->where('tipe', $request->tipe);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_pelatihan', 'like', "%{$search}%")
                  ->orWhere('instruktur', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%");
            });
        }

        // Order by date
        $pelatihans = $query->orderBy('tanggal_mulai', 'desc')->paginate(9);

        // Statistics
        $stats = [
            'total' => Pelatihan::whereIn('status', ['open', 'closed', 'completed'])->count(),
            'aktif' => Pelatihan::where('status', 'open')->count(),
            'selesai' => Pelatihan::where('status', 'completed')->count(),
            'peserta' => Pelatihan::sum('peserta_terdaftar')
        ];

        return view('landingpage.pelatihan', compact('pelatihans', 'stats'));
    }

    public function pelatihanShow(Pelatihan $pelatihan)
    {
        // Load relationships
        $pelatihan->load('users');

        return view('landingpage.pelatihan-detail', compact('pelatihan'));
    }

    public function galeri()
    {
        // TODO: Implement galeri logic
        return view('landingpage.galeri');
    }

    public function peta()
    {
        // TODO: Implement peta logic
        return view('landingpage.peta');
    }

    public function kontak()
    {
        // TODO: Implement kontak logic
        return view('landingpage.kontak');
    }
}
