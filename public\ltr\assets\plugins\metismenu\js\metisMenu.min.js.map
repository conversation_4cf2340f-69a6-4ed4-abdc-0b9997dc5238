{"version": 3, "sources": ["../src/util.js", "../src/index.js"], "names": ["$", "TRANSITION_END", "<PERSON><PERSON>", "triggerTransitionEnd", "element", "trigger", "supportsTransitionEnd", "Boolean", "fn", "mmEmulateTransitionEnd", "transitionEndEmulator", "event", "special", "bindType", "delegateType", "handle", "target", "is", "this", "handleObj", "handler", "apply", "arguments", "duration", "_this", "called", "one", "setTimeout", "NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON>", "toggle", "preventDefault", "triggerElement", "parentTrigger", "subMenu", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "MetisMenu", "config", "_extends", "transitioning", "init", "self", "conf", "el", "addClass", "find", "children", "attr", "parents", "has", "not", "on", "e", "eTar", "paRent", "parent", "sibLi", "siblings", "sib<PERSON><PERSON>ger", "hasClass", "removeActive", "setActive", "onTransitionStart", "li", "ul", "length", "show", "removeClass", "hide", "elem", "startEvent", "isDefaultPrevented", "toggleElem", "height", "setTransitioning", "scrollHeight", "_this2", "offsetHeight", "complete", "onTransitionEnd", "css", "isTransitioning", "dispose", "removeData", "off", "jQueryInterface", "each", "$this", "data", "undefined", "Error", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;sfAEA,IAAeA,EACPC,EAEAC,EAHFA,GACED,EAAiB,gBAEjBC,EAAO,CACXD,eAAgB,kBAEhBE,qBAHW,SAGUC,GACnBJ,EAAEI,GAASC,QAAQJ,IAGrBK,sBAPW,WAQT,OAAOC,QAAQN,MAXND,EAwDZA,GARGQ,GAAGC,uBAAyBC,EAE9BV,EAAEW,MAAMC,QAAQV,EAAKD,gBAlCd,CACLY,SAAUZ,EACVa,aAAcb,EACdc,OAHK,SAGEJ,GACL,GAAIX,EAAEW,EAAMK,QAAQC,GAAGC,MACrB,OAAOP,EACJQ,UACAC,QACAC,MAAMH,KAAMI,aA+BhBpB,GAxBP,SAASQ,EAAsBa,GAAU,IAAAC,EAAAN,KACnCO,GAAS,EAYb,OAVAzB,EAAEkB,MAAMQ,IAAIxB,EAAKD,eAAgB,WAC/BwB,GAAS,IAGXE,WAAW,WACJF,GACHvB,EAAKC,qBAAqBqB,IAE3BD,GAEIL,KC3CX,IAAMU,EAAO,YACPC,EAAW,YACXC,EAAS,IAAOD,EAEhBE,EAAqB/B,EAAEQ,GAAGoB,GAG1BI,EAAU,CACdC,QAAQ,EACRC,gBAAgB,EAChBC,eAAgB,IAChBC,cAAe,KACfC,QAAS,MAGLC,EAAQ,CACZC,KAAI,OAAST,EACbU,MAAK,QAAUV,EACfW,KAAI,OAASX,EACbY,OAAM,SAAWZ,EACjBa,eAAc,QAAUb,EAjBL,aAoBfc,EACG,YADHA,EAEI,YAFJA,EAGE,UAHFA,EAIM,cAJNA,EAKQ,gBAIRC,EAAAA,WAEJ,SAAAA,EAAYzC,EAAS0C,GACnB5B,KAAKd,QAAUA,EACfc,KAAK4B,OAALC,EAAA,GACKf,EADL,GAEKc,GAEL5B,KAAK8B,cAAgB,KAErB9B,KAAK+B,kCAGPA,KAAA,WACE,IAAMC,EAAOhC,KACPiC,EAAOjC,KAAK4B,OACZM,EAAKpD,EAAEkB,KAAKd,SAElBgD,EAAGC,SAAST,GAEZQ,EAAGE,KAAQH,EAAKf,cAAhB,IAAiCQ,GAC9BW,SAASJ,EAAKhB,gBACdqB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAQH,EAAKf,cAAhB,IAAiCQ,GAC9Ba,QAAQN,EAAKf,eACbiB,SAAST,GAEZQ,EAAGE,KAAQH,EAAKf,cAAhB,IAAiCQ,GAC9Ba,QAAQN,EAAKf,eACbmB,SAASJ,EAAKhB,gBACdqB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAQH,EAAKf,cAAhB,IAAiCQ,GAC9Bc,IAAIP,EAAKd,SACTkB,SAASJ,EAAKd,SACdgB,SAAYT,EAHf,IAGqCA,GAErCQ,EACGE,KAAKH,EAAKf,eACVuB,IAFH,IAEWf,GACRc,IAAIP,EAAKd,SACTkB,SAASJ,EAAKd,SACdgB,SAAST,GAEZQ,EACGE,KAAKH,EAAKf,eAEVmB,SAASJ,EAAKhB,gBACdyB,GAAGtB,EAAMK,eAAgB,SAAUkB,GAClC,IAAMC,EAAO9D,EAAEkB,MAEf,GAAmC,SAA/B4C,EAAKN,KAAK,iBAAd,CAIIL,EAAKjB,gBAAwC,MAAtB4B,EAAKN,KAAK,SACnCK,EAAE3B,iBAGJ,IAAM6B,EAASD,EAAKE,OAAOb,EAAKf,eAC1B6B,EAAQF,EAAOG,SAASf,EAAKf,eAC7B+B,EAAaF,EAAMV,SAASJ,EAAKhB,gBAEnC4B,EAAOK,SAASxB,IAClBkB,EAAKN,KAAK,gBAAiB,SAC3BN,EAAKmB,aAAaN,KAElBD,EAAKN,KAAK,gBAAiB,QAC3BN,EAAKoB,UAAUP,GACXZ,EAAKlB,SACPiB,EAAKmB,aAAaJ,GAClBE,EAAWX,KAAK,gBAAiB,WAIjCL,EAAKoB,mBACPpB,EAAKoB,kBAAkBV,SAK/BS,UAAA,SAAUE,GACRxE,EAAEwE,GAAInB,SAAST,GACf,IAAM6B,EAAKzE,EAAEwE,GAAIjB,SAASrC,KAAK4B,OAAOT,SACtB,EAAZoC,EAAGC,SAAeD,EAAGL,SAASxB,IAChC1B,KAAKyD,KAAKF,MAIdJ,aAAA,SAAaG,GACXxE,EAAEwE,GAAII,YAAYhC,GAClB,IAAM6B,EAAKzE,EAAEwE,GAAIjB,SAAYrC,KAAK4B,OAAOT,QAA9B,IAAyCO,GACpC,EAAZ6B,EAAGC,QACLxD,KAAK2D,KAAKJ,MAIdE,KAAA,SAAKvE,GAAS,IAAAoB,EAAAN,KACZ,IAAIA,KAAK8B,gBAAiBhD,EAAEI,GAASgE,SAASxB,GAA9C,CAGA,IAAMkC,EAAO9E,EAAEI,GAET2E,EAAa/E,EAAEsC,MAAMA,EAAMC,MAGjC,GAFAuC,EAAKzE,QAAQ0E,IAETA,EAAWC,qBAAf,CAMA,GAFAF,EAAKd,OAAO9C,KAAK4B,OAAOV,eAAeiB,SAAST,GAE5C1B,KAAK4B,OAAOb,OAAQ,CACtB,IAAMgD,EAAaH,EAAKd,OAAO9C,KAAK4B,OAAOV,eAAe8B,WAAWX,SAAYrC,KAAK4B,OAAOT,QAA1E,IAAqFO,GACxG1B,KAAK2D,KAAKI,GAGZH,EACGF,YAAYhC,GACZS,SAAST,GACTsC,OAAO,GAEVhE,KAAKiE,kBAAiB,GAiBtBL,EACGI,OAAO9E,EAAQ,GAAGgF,cAClB1D,IAAIxB,EAAKD,eAjBK,WAEVuB,EAAKsB,QAAWtB,EAAKpB,UAG1B0E,EACGF,YAAYhC,GACZS,SAAYT,EAFf,IAEqCA,GAClCsC,OAAO,IAEV1D,EAAK2D,kBAAiB,GAEtBL,EAAKzE,QAAQiC,EAAME,UAMlB/B,uBA1KqB,UA6K1BoE,KAAA,SAAKzE,GAAS,IAAAiF,EAAAnE,KACZ,IACEA,KAAK8B,eAAkBhD,EAAEI,GAASgE,SAASxB,GAD7C,CAMA,IAAMkC,EAAO9E,EAAEI,GAET2E,EAAa/E,EAAEsC,MAAMA,EAAMG,MAGjC,GAFAqC,EAAKzE,QAAQ0E,IAETA,EAAWC,qBAAf,CAIAF,EAAKd,OAAO9C,KAAK4B,OAAOV,eAAewC,YAAYhC,GAEnDkC,EAAKI,OAAOJ,EAAKI,UAAU,GAAGI,aAE9BR,EACGzB,SAAST,GACTgC,YAAYhC,GACZgC,YAAYhC,GAEf1B,KAAKiE,kBAAiB,GAEtB,IAAMI,EAAW,WAEVF,EAAKvC,QAAWuC,EAAKjF,UAGtBiF,EAAKrC,eAAiBqC,EAAKvC,OAAO0C,iBACpCH,EAAKvC,OAAO0C,kBAGdH,EAAKF,kBAAiB,GACtBL,EAAKzE,QAAQiC,EAAMI,QAEnBoC,EACGF,YAAYhC,GACZS,SAAST,KAGQ,IAAlBkC,EAAKI,UAA0C,SAAxBJ,EAAKW,IAAI,WAClCF,IAEAT,EACGI,OAAO,GACPxD,IAAIxB,EAAKD,eAAgBsF,GACzB9E,uBA/NmB,UAmO1B0E,iBAAA,SAAiBO,GACfxE,KAAK8B,cAAgB0C,KAGvBC,QAAA,WACE3F,EAAE4F,WAAW1E,KAAKd,QAASyB,GAE3B7B,EAAEkB,KAAKd,SACJkD,KAAKpC,KAAK4B,OAAOV,eAEjBmB,SAASrC,KAAK4B,OAAOX,gBACrB0D,IAAIvD,EAAMK,gBAEbzB,KAAK8B,cAAgB,KACrB9B,KAAK4B,OAAS,KACd5B,KAAKd,QAAU,QAGV0F,gBAAP,SAAuBhD,GAErB,OAAO5B,KAAK6E,KAAK,WACf,IAAMC,EAAQhG,EAAEkB,MACZ+E,EAAOD,EAAMC,KAAKpE,GAChBsB,EAAIJ,EAAA,GACLf,EADK,GAELgE,EAAMC,OAFD,GAGc,iBAAXnD,GAAuBA,EAASA,EAAS,IAQtD,GALKmD,IACHA,EAAO,IAAIpD,EAAU3B,KAAMiC,GAC3B6C,EAAMC,KAAKpE,EAAUoE,IAGD,iBAAXnD,EAAqB,CAC9B,QAAqBoD,IAAjBD,EAAKnD,GACP,MAAM,IAAIqD,MAAJ,oBAA8BrD,EAA9B,KAERmD,EAAKnD,WA9OPD,UAyPN7C,EAAEQ,GAAGoB,GAAQiB,EAAUiD,gBACvB9F,EAAEQ,GAAGoB,GAAMwE,YAAcvD,EACzB7C,EAAEQ,GAAGoB,GAAMyE,WAAa,WAGtB,OADArG,EAAEQ,GAAGoB,GAAQG,EACNc,EAAUiD", "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = {\n      ...Default,\n      ...config,\n    };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n    const el = $(this.element);\n\n    el.addClass(ClassName.METIS); // add metismenu class to element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    el\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    el\n      .find(conf.parentTrigger)\n      // .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n\n        if (conf.preventDefault && eTar.attr('href') === '#') {\n          e.preventDefault();\n        }\n\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLi = paRent.siblings(conf.parentTrigger);\n        const sibTrigger = sibLi.children(conf.triggerElement);\n\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.removeActive(paRent);\n        } else {\n          eTar.attr('aria-expanded', 'true');\n          self.setActive(paRent);\n          if (conf.toggle) {\n            self.removeActive(sibLi);\n            sibTrigger.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  setActive(li) {\n    $(li).addClass(ClassName.ACTIVE);\n    const ul = $(li).children(this.config.subMenu);\n    if (ul.length > 0 && !ul.hasClass(ClassName.SHOW)) {\n      this.show(ul);\n    }\n  }\n\n  removeActive(li) {\n    $(li).removeClass(ClassName.ACTIVE);\n    const ul = $(li).children(`${this.config.subMenu}.${ClassName.SHOW}`);\n    if (ul.length > 0) {\n      this.hide(ul);\n    }\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      // .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off(Event.CLICK_DATA_API);\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"]}