# Panduan Integrasi API Wilayah Indonesia

## Konfigurasi API

API wilayah Indonesia telah dikonfigurasi di file `.env`:

```env
# API Wilayah Indonesia 
WILAYAH_API_BASE_URL=https://wilayah.id/api 
WILAYAH_PROVINCES_URL=https://wilayah.id/api/provinces.json 
WILAYAH_REGENCIES_URL=https://wilayah.id/api/regencies 
WILAYAH_DISTRICTS_URL=https://wilayah.id/api/districts 
WILAYAH_VILLAGES_URL=https://wilayah.id/api/villages
```

## Struktur API Endpoints

### 1. Provinsi
- **URL**: `https://wilayah.id/api/provinces.json`
- **Method**: GET
- **Response**: Array of provinces
```json
[
  {
    "code": "11",
    "name": "ACEH"
  },
  {
    "code": "12", 
    "name": "SUMATERA UTARA"
  }
]
```

### 2. Kabupaten/Kota
- **URL**: `https://wilayah.id/api/regencies/{province_code}.json`
- **Method**: GET
- **Example**: `https://wilayah.id/api/regencies/33.json` (Jawa Tengah)
- **Response**: Array of regencies
```json
[
  {
    "code": "33.01",
    "name": "KABUPATEN CILACAP"
  },
  {
    "code": "33.02",
    "name": "KABUPATEN BANYUMAS"
  }
]
```

### 3. Kecamatan
- **URL**: `https://wilayah.id/api/districts/{regency_code}.json`
- **Method**: GET
- **Example**: `https://wilayah.id/api/districts/33.06.json` (Kabupaten Purworejo)
- **Response**: Array of districts
```json
[
  {
    "code": "33.06.01",
    "name": "BAGELEN"
  },
  {
    "code": "33.06.02", 
    "name": "BANYUURIP"
  }
]
```

### 4. Desa/Kelurahan
- **URL**: `https://wilayah.id/api/villages/{district_code}.json`
- **Method**: GET
- **Example**: `https://wilayah.id/api/villages/33.06.01.json` (Kecamatan Bagelen)
- **Response**: Array of villages
```json
[
  {
    "code": "33.06.01.2001",
    "name": "BAGELEN"
  },
  {
    "code": "33.06.01.2002",
    "name": "BENER"
  }
]
```

## Implementasi di Form Registrasi

### HTML Structure
Form registrasi memiliki 2 set dropdown wilayah:

1. **Alamat Pribadi**:
   - `provinsi_pribadi`
   - `kabupaten_pribadi` 
   - `kecamatan_pribadi`
   - `desa_pribadi`

2. **Alamat Usaha**:
   - `provinsi_usaha`
   - `kabupaten_usaha`
   - `kecamatan_usaha`
   - `desa_usaha`

### JavaScript Functions

#### 1. Load Provinces
```javascript
async function loadProvinces() {
    const data = await fetchWilayahData(WILAYAH_API.provinces);
    
    ['provinsi_pribadi', 'provinsi_usaha'].forEach(id => {
        const select = document.getElementById(id);
        select.innerHTML = '<option value="">Pilih Provinsi</option>';
        
        data.forEach(province => {
            const option = document.createElement('option');
            option.value = province.code;
            option.textContent = province.name;
            select.appendChild(option);
        });
    });
}
```

#### 2. Load Regencies
```javascript
async function loadRegencies(provinceCode, type) {
    const selectId = `kabupaten_${type}`;
    const select = document.getElementById(selectId);
    
    setLoading(select, true);
    clearDependentDropdowns('kabupaten', type);
    
    const data = await fetchWilayahData(`${WILAYAH_API.regencies}/${provinceCode}.json`);
    
    select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
    data.forEach(regency => {
        const option = document.createElement('option');
        option.value = regency.code;
        option.textContent = regency.name;
        select.appendChild(option);
    });
    
    setLoading(select, false);
}
```

#### 3. Event Listeners
```javascript
// Province change handlers
document.getElementById('provinsi_pribadi').addEventListener('change', function() {
    if (this.value) {
        loadRegencies(this.value, 'pribadi');
    } else {
        clearDependentDropdowns('kabupaten', 'pribadi');
    }
});
```

## Fitur Tambahan

### 1. Loading State
- Menampilkan "Memuat..." saat fetch data
- Disable dropdown saat loading
- Clear dependent dropdowns

### 2. Error Handling
- Try-catch untuk handle network errors
- Fallback ke array kosong jika API gagal
- Console error logging

### 3. Form Validation
- Validasi semua field required
- Validasi NIK 16 digit
- Validasi password confirmation
- Visual feedback dengan border merah

### 4. Legalitas Usaha (Opsional)
- Checkbox untuk toggle input fields
- NIB, NPWP, Sertifikat Halal, SIUP
- Legalitas lainnya dengan nama dan nomor

## Cara Testing

1. **Buka halaman registrasi**: http://127.0.0.1:8000/registrasi

2. **Test dropdown cascade**:
   - Pilih provinsi → kabupaten/kota akan terisi
   - Pilih kabupaten/kota → kecamatan akan terisi  
   - Pilih kecamatan → desa/kelurahan akan terisi

3. **Test untuk kedua alamat**:
   - Alamat pribadi dan alamat usaha bekerja independen
   - Bisa pilih provinsi berbeda untuk keduanya

4. **Test legalitas**:
   - Centang checkbox legalitas → input field muncul
   - Uncheck → input field hilang dan value di-clear

5. **Test form validation**:
   - Submit tanpa isi → error validation
   - NIK bukan 16 digit → error
   - Password tidak sama → error

## Kode Referensi

File lengkap implementasi ada di:
- `resources/views/landingpage/registrasi.blade.php`
- `kodeformregis.php` (referensi struktur)

## Catatan Penting

1. **API Rate Limiting**: API wilayah.id mungkin memiliki rate limiting, gunakan dengan bijak
2. **Caching**: Pertimbangkan untuk cache data provinsi di localStorage
3. **Fallback**: Siapkan data backup jika API tidak tersedia
4. **Performance**: Load data secara asynchronous untuk UX yang baik
5. **Validation**: Selalu validasi data di backend juga, jangan hanya di frontend
