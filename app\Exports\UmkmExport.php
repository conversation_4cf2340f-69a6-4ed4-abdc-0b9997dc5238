<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class UmkmExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithEvents, WithMapping
{
    protected $umkms;

    public function __construct($umkms = null)
    {
        $this->umkms = $umkms ?: User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->get();
    }

    public function collection()
    {
        return $this->umkms;
    }

    public function map($umkm): array
    {
        static $no = 1;
        
        // Format alamat pribadi
        $alamatPribadi = '';
        if ($umkm->profil) {
            $alamatPribadi = $umkm->profil->alamat_lengkap . ', ' .
                           $umkm->profil->desa . ', ' .
                           $umkm->profil->kecamatan . ', ' .
                           $umkm->profil->kabupaten . ', ' .
                           $umkm->profil->provinsi;
        }

        // Format alamat usaha
        $alamatUsaha = '';
        if ($umkm->usaha) {
            $alamatUsaha = $umkm->usaha->alamat_lengkap_usaha . ', ' .
                         $umkm->usaha->desa_usaha . ', ' .
                         $umkm->usaha->kecamatan_usaha . ', ' .
                         $umkm->usaha->kabupaten_usaha . ', ' .
                         $umkm->usaha->provinsi_usaha;
        }

        // Format legalitas
        $legalitas = '';
        if ($umkm->legalitas && $umkm->legalitas->count() > 0) {
            $legalitas = $umkm->legalitas->map(function ($legal) {
                return $legal->nama_legalitas . ': ' . $legal->nomor_legalitas;
            })->implode('; ');
        }

        // Format bidang usaha
        $bidangUsaha = '';
        if ($umkm->usaha && $umkm->usaha->bidang_usaha) {
            $bidangMap = [
                'makanan_minuman' => 'Makanan & Minuman',
                'kerajinan_tangan' => 'Kerajinan Tangan',
                'perdagangan' => 'Perdagangan',
                'jasa' => 'Jasa'
            ];
            $bidangUsaha = $bidangMap[$umkm->usaha->bidang_usaha] ?? ucwords(str_replace('_', ' ', $umkm->usaha->bidang_usaha));
        }

        // Format status verifikasi
        $statusVerifikasi = '';
        switch ($umkm->verification_status ?? 'pending') {
            case 'verified':
                $statusVerifikasi = 'Terverifikasi';
                break;
            case 'rejected':
                $statusVerifikasi = 'Ditolak';
                break;
            default:
                $statusVerifikasi = 'Pending';
                break;
        }

        return [
            $no++,
            $umkm->nama ?? 'Tidak ada nama',
            $umkm->email ?? 'Tidak ada email',
            "'" . ($umkm->profil->nik ?? 'Tidak ada NIK'), // Add single quote to force string
            "'" . ($umkm->no_hp ?? 'Tidak ada nomor HP'), // Add single quote to force string
            $alamatPribadi ?: 'Alamat belum diisi',
            $umkm->usaha->nama_usaha ?? 'Nama usaha belum diisi',
            $umkm->usaha->nama_merk ?? ($umkm->usaha->merk_usaha ?? 'Merk belum diisi'),
            $bidangUsaha ?: 'Bidang belum diisi',
            $alamatUsaha ?: 'Alamat usaha belum diisi',
            $legalitas ?: 'Belum ada legalitas',
            $statusVerifikasi,
            $umkm->created_at ? $umkm->created_at->format('d/m/Y') : 'Tidak diketahui'
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'Nama Pemilik',
            'Email',
            'NIK',
            'Nomor HP',
            'Alamat Pribadi',
            'Nama Usaha',
            'Merk Usaha',
            'Bidang Usaha',
            'Alamat Usaha',
            'Legalitas',
            'Status Verifikasi',
            'Tanggal Daftar'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,   // No
            'B' => 25,  // Nama Pemilik
            'C' => 30,  // Email
            'D' => 20,  // NIK
            'E' => 15,  // Nomor HP
            'F' => 40,  // Alamat Pribadi
            'G' => 30,  // Nama Usaha
            'H' => 25,  // Merk Usaha
            'I' => 20,  // Bidang Usaha
            'J' => 40,  // Alamat Usaha
            'K' => 30,  // Legalitas
            'L' => 15,  // Status Verifikasi
            'M' => 15,  // Tanggal Daftar
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E2E2',
                    ],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Data UMKM';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Add title row
                $event->sheet->getDelegate()->insertNewRowBefore(1, 2);
                $event->sheet->getDelegate()->setCellValue('A1', 'DATA UMKM KABUPATEN PURWOREJO');
                $event->sheet->getDelegate()->mergeCells('A1:M1');
                
                // Style title
                $event->sheet->getDelegate()->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 16,
                        'color' => [
                            'argb' => 'FFFFFFFF',
                        ],
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'argb' => 'FF4472C4',
                        ],
                    ],
                ]);

                // Add borders to all data
                $highestRow = $event->sheet->getDelegate()->getHighestRow();
                $highestColumn = $event->sheet->getDelegate()->getHighestColumn();
                
                $event->sheet->getDelegate()->getStyle('A3:' . $highestColumn . $highestRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['argb' => 'FF000000'],
                        ],
                    ],
                ]);

                // Style header row (now row 3)
                $event->sheet->getDelegate()->getStyle('A3:M3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'argb' => 'FFE2E2E2',
                        ],
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                // Auto-fit row height
                for ($i = 1; $i <= $highestRow; $i++) {
                    $event->sheet->getDelegate()->getRowDimension($i)->setRowHeight(-1);
                }
            },
        ];
    }
}
