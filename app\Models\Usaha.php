<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Usaha extends Model
{
    use HasFactory;

    protected $table = 'usaha';

    protected $fillable = [
        'user_id',
        'nama_usaha',
        'nama_merk',
        'bidang_usaha',
        'deskripsi',
        'media_sosial',
        'provinsi_usaha',
        'kabupaten_usaha',
        'kecamatan_usaha',
        'desa_usaha',
        'alamat_lengkap_usaha',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        return $this->alamat_lengkap_usaha . ', ' .
               $this->desa_usaha . ', ' .
               $this->kecamatan_usaha . ', ' .
               $this->kabupaten_usaha . ', ' .
               $this->provinsi_usaha;
    }
}
