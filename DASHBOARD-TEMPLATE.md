# Dashboard Template PLUT Purworejo

Template dashboard yang diadaptasi dari template LTR untuk aplikasi PLUT Purworejo.

## 📁 File Template

### Admin Dashboard
- **File**: `resources/views/admin/dashboard-new.blade.php`
- **Route**: `/admin/dashboard-new` atau `/test-admin-dashboard`
- **Fitur**: 
  - Stats cards dengan mini charts
  - Tabel UMKM terbaru
  - Timeline aktivitas
  - Chart statistik UMKM
  - Notifikasi dropdown

### UMKM Dashboard
- **File**: `resources/views/umkm/dashboard-new.blade.php`
- **Route**: `/umkm/dashboard-new` atau `/test-umkm-dashboard`
- **Fitur**:
  - Stats cards untuk produk, pesanan, pendapatan, rating
  - Navigasi khusus UMKM
  - Notifikasi pesanan dan pelatihan

## 🎨 Asset Template

### CSS Files
```
public/ltr/assets/css/
├── bootstrap.min.css          # Bootstrap 5 framework
├── bootstrap-extended.css     # Extended Bootstrap components
├── style.css                  # Main template styles
├── icons.css                  # Icon fonts
├── semi-dark.css             # Semi-dark theme
├── dark-theme.css            # Dark theme
├── light-theme.css           # Light theme
└── header-colors.css         # Header color variations
```

### JavaScript Files
```
public/ltr/assets/js/
├── bootstrap.bundle.min.js    # Bootstrap JS
├── jquery.min.js             # jQuery library
├── app.js                    # Main app JS
└── index2.js                 # Dashboard specific JS
```

### Plugins
```
public/ltr/assets/plugins/
├── apexcharts-bundle/        # Chart library
├── metismenu/               # Sidebar navigation
├── simplebar/               # Custom scrollbar
├── perfect-scrollbar/       # Perfect scrollbar
└── notifications/           # Notification sounds
```

## 🚀 Cara Menggunakan

### 1. Akses Dashboard
- **Admin**: `http://localhost:8000/admin/dashboard-new`
- **UMKM**: `http://localhost:8000/umkm/dashboard-new`
- **Test Admin**: `http://localhost:8000/test-admin-dashboard`
- **Test UMKM**: `http://localhost:8000/test-umkm-dashboard`

### 2. Fitur yang Tersedia

#### Header
- Search bar global
- Notifikasi dropdown dengan badge counter
- User profile dropdown
- Mobile toggle untuk sidebar

#### Sidebar
- **Admin**: Dashboard, UMKM Management, Users, Content, Reports, Settings
- **UMKM**: Dashboard, Profile, Products, Orders, Training, Reports, Promotion, Help

#### Stats Cards
- **Admin**: Total UMKM, UMKM Aktif, Menunggu Verifikasi, Pelatihan Aktif
- **UMKM**: Total Produk, Pesanan Bulan Ini, Pendapatan, Rating

#### Charts
- Mini sparkline charts di stats cards
- Bar chart untuk statistik bulanan
- Radial chart untuk progress indicators

### 3. Customization

#### Mengubah Theme
Template mendukung 3 theme:
```html
<html class="light-theme">   <!-- Light theme -->
<html class="dark-theme">    <!-- Dark theme -->
<html class="semi-dark">     <!-- Semi-dark theme (default) -->
```

#### Mengubah Warna Header
Tambahkan class di `<body>`:
```html
<body class="bg-theme bg-theme1">  <!-- Blue header -->
<body class="bg-theme bg-theme2">  <!-- Green header -->
<body class="bg-theme bg-theme3">  <!-- Purple header -->
```

#### Menambah Stats Card
```html
<div class="col">
    <div class="card overflow-hidden radius-10">
        <div class="card-body p-2">
            <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                <div class="w-50 p-3 bg-light-primary">
                    <p>Judul Statistik</p>
                    <h4 class="text-primary">1,234</h4>
                </div>
                <div class="w-50 bg-primary p-3">
                    <p class="mb-3 text-white">+ 12% <i class="bi bi-arrow-up"></i></p>
                    <div id="chartX"></div>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### Menambah Chart
```javascript
var options = {
    series: [{
        name: 'Data',
        data: [31, 40, 28, 51, 42, 109, 100]
    }],
    chart: {
        height: 350,
        type: 'area'
    },
    colors: ['#0d6efd']
};
var chart = new ApexCharts(document.querySelector("#chartContainer"), options);
chart.render();
```

## 🔧 Troubleshooting

### Asset Tidak Load
1. Pastikan folder `ltr` ada di `public/`
2. Periksa path asset di template: `{{ asset('ltr/assets/css/style.css') }}`
3. Clear cache Laravel: `php artisan cache:clear`

### Chart Tidak Muncul
1. Pastikan ApexCharts loaded: `public/ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js`
2. Periksa console browser untuk error JavaScript
3. Pastikan element target chart ada: `<div id="chart1"></div>`

### Sidebar Tidak Responsive
1. Pastikan MetisMenu loaded: `public/ltr/assets/plugins/metismenu/js/metisMenu.min.js`
2. Periksa Bootstrap JS loaded
3. Pastikan `app.js` loaded terakhir

## 📱 Responsive Design

Template sudah responsive untuk:
- **Desktop**: Full sidebar dan content
- **Tablet**: Collapsible sidebar
- **Mobile**: Hidden sidebar dengan toggle button

## 🎯 Next Steps

1. **Integrasi Data Real**: Ganti data dummy dengan data dari database
2. **Chart Dinamis**: Implementasi chart dengan data real-time
3. **Notifikasi Real**: Implementasi notifikasi real-time dengan WebSocket
4. **User Management**: Implementasi sistem login dan role management
5. **Export Features**: Implementasi export data ke PDF/Excel

## 📞 Support

Jika ada masalah dengan template, periksa:
1. Console browser untuk error JavaScript
2. Network tab untuk asset yang gagal load
3. Laravel log untuk error server-side

Template ini siap untuk development dan dapat dikustomisasi sesuai kebutuhan aplikasi PLUT Purworejo.
