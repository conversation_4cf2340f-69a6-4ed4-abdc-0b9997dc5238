<?php

namespace App\Http\Controllers;

use App\Models\Pelatihan;
use Illuminate\Http\Request;

class PelatihanController extends Controller
{
    public function index()
    {
        // Ambil 2 pelatihan unggulan
        $featuredTrainings = Pelatihan::where('is_featured', true)
            ->where('status', 'open')
            ->limit(2)
            ->get();

        // Query untuk semua pelatihan dengan filter
        $pelatihans = Pelatihan::query()
            ->when(request('kategori'), function($query) {
                return $query->where('kategori', request('kategori'));
            })
            ->when(request('status'), function($query) {
                return $query->where('status', request('status'));
            })
            ->when(request('search'), function($query) {
                return $query->where('nama_pelatihan', 'like', '%'.request('search').'%')
                    ->orWhere('deskripsi', 'like', '%'.request('search').'%');
            })
            ->orderBy('tanggal_mulai', 'asc')
            ->paginate(6);

        return view('landingpage.pelatihan', compact('featuredTrainings', 'pelatihans'));
    }

    public function show($id)
    {
        $pelatihan = Pelatihan::findOrFail($id);
        return view('landingpage.pelatihan-detail', compact('pelatihan'));
    }
}