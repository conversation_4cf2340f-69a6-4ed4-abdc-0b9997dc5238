<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UmkmRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Data User
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'no_hp' => 'required|string|max:20',

            // Data Profil Pribadi
            'nik' => 'required|string|size:16|unique:profils,nik',
            'jenis_kelamin' => 'required|in:Laki-laki,Perempuan',

            // Alamat Pribadi
            'provinsi' => 'required|string|max:100',
            'kabupaten' => 'required|string|max:100',
            'kecamatan' => 'required|string|max:100',
            'desa' => 'required|string|max:100',
            'alamat_lengkap' => 'required|string|max:500',

            // Data Usaha
            'nama_usaha' => 'required|string|max:255',
            'nama_merk' => 'nullable|string|max:255',
            'bidang_usaha' => 'required|in:makanan_minuman,kerajinan_tangan,perdagangan,jasa',
            'deskripsi' => 'nullable|string|max:1000',

            // Media Sosial (Optional)
            'media_sosial_types.*' => 'nullable|string|in:Instagram,Facebook,WhatsApp,TikTok,YouTube,Website',
            'instagram_username' => 'nullable|string|max:255',
            'facebook_page' => 'nullable|string|max:255',
            'whatsapp_number' => 'nullable|string|max:20',
            'tiktok_username' => 'nullable|string|max:255',
            'youtube_channel' => 'nullable|string|max:255',
            'website_url' => 'nullable|url|max:255',

            // Alamat Usaha
            'kecamatan_usaha' => 'required|string|max:100',
            'desa_usaha' => 'required|string|max:100',
            'alamat_lengkap_usaha' => 'required|string|max:500',

            // Legalitas (Optional)
            'legalitas_types.*' => 'nullable|string|in:NIB,SIUP,NPWP,TDP,Lainnya',
            'nib_nomor' => 'nullable|string|max:255',
            'siup_nomor' => 'nullable|string|max:255',
            'npwp_nomor' => 'nullable|string|max:255',
            'tdp_nomor' => 'nullable|string|max:255',
            'lainnya_nama' => 'nullable|string|max:255',
            'lainnya_nomor' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'nik.required' => 'NIK wajib diisi.',
            'nik.size' => 'NIK harus terdiri dari 16 digit.',
            'nik.unique' => 'NIK sudah terdaftar dalam sistem. Silakan gunakan NIK yang berbeda.',
            'email.unique' => 'Email sudah terdaftar. Silakan gunakan email yang berbeda.',
            'password.min' => 'Password minimal 8 karakter.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'tanggal_lahir.before' => 'Tanggal lahir harus sebelum hari ini.',
            'jenis_kelamin.in' => 'Jenis kelamin harus Laki-laki atau Perempuan.',
            'jenis_usaha.in' => 'Jenis usaha harus mikro, kecil, atau menengah.',
            'bidang_usaha.in' => 'Bidang usaha tidak valid.',
            'klasifikasi.in' => 'Klasifikasi usaha harus industri, perdagangan, atau jasa.',
            'website.url' => 'Format website tidak valid.',
            'tahun_berdiri.max' => 'Tahun berdiri tidak boleh lebih dari tahun sekarang.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'nama' => 'nama lengkap',
            'no_hp' => 'nomor HP',
            'nik' => 'NIK',
            'alamat_lengkap' => 'alamat lengkap',
            'tempat_lahir' => 'tempat lahir',
            'tanggal_lahir' => 'tanggal lahir',
            'jenis_kelamin' => 'jenis kelamin',
            'pendidikan_terakhir' => 'pendidikan terakhir',
            'pekerjaan_lain' => 'pekerjaan lain',
            'nama_usaha' => 'nama usaha',
            'nama_merk' => 'nama merk',
            'alamat_usaha' => 'alamat usaha',
            'jenis_usaha' => 'jenis usaha',
            'bidang_usaha' => 'bidang usaha',
            'email_usaha' => 'email usaha',
            'media_sosial' => 'media sosial',
            'jumlah_karyawan' => 'jumlah karyawan',
            'omzet_bulanan' => 'omzet bulanan',
            'modal_awal' => 'modal awal',
            'tahun_berdiri' => 'tahun berdiri',
        ];
    }
}
