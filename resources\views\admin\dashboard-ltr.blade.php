@extends('layouts.admin')

@section('title', 'Dashboard Admin - PLUT Purworejo')

@section('content')
<!-- Breadcrumb -->
<div class="mb-3 page-breadcrumb d-none d-sm-flex align-items-center">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="p-0 mb-0 breadcrumb">
                <li class="breadcrumb-item"><a href="javascript: void(0);"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary">
                <i class="bi bi-download"></i> Export Laporan
            </button>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-end">
                <a class="dropdown-item" href="#"><i class="bi bi-file-excel"></i> Export Excel</a>
                <a class="dropdown-item" href="#"><i class="bi bi-file-pdf"></i> Export PDF</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#"><i class="bi bi-printer"></i> Print</a>
            </div>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="overflow-hidden card radius-10">
            <div class="p-2 card-body">
                <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                    <div class="p-3 w-50 bg-light-primary">
                        <p>Total UMKM</p>
                        <h4 class="text-primary">{{ $totalUmkm }}</h4>
                    </div>
                    <div class="p-3 w-50 bg-primary">
                        <span class="text-white fs-2"><i class="bi bi-shop"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="overflow-hidden card radius-10">
            <div class="p-2 card-body">
                <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                    <div class="p-3 w-50 bg-light-success">
                        <p>UMKM Terverifikasi</p>
                        <h4 class="text-success">{{ $umkmVerified }}</h4>
                    </div>
                    <div class="p-3 w-50 bg-success">
                        <span class="text-white fs-2"><i class="bi bi-check-circle"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="overflow-hidden card radius-10">
            <div class="p-2 card-body">
                <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                    <div class="p-3 w-50 bg-light-warning">
                        <p>Menunggu Verifikasi</p>
                        <h4 class="text-warning">{{ $umkmPending }}</h4>
                    </div>
                    <div class="p-3 w-50 bg-warning">
                        <span class="text-white fs-2"><i class="bi bi-clock"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="overflow-hidden card radius-10">
            <div class="p-2 card-body">
                <div class="overflow-hidden d-flex align-items-stretch justify-content-between radius-10">
                    <div class="p-3 w-50 bg-light-info">
                        <p>Total Berita</p>
                        <h4 class="text-info">{{ $totalBerita }}</h4>
                    </div>
                    <div class="p-3 w-50 bg-info">
                        <span class="text-white fs-2"><i class="bi bi-newspaper"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end row-->

<!-- Recent UMKM Registrations -->
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card radius-10">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <div>
                        <h6 class="mb-0">Pendaftaran UMKM Terbaru</h6>
                    </div>
                    <div class="dropdown ms-auto">
                        <a class="dropdown-toggle dropdown-toggle-nocaret" href="#" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-horizontal-rounded font-22 text-option"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/umkm">Lihat Semua</a></li>
                            <li><a class="dropdown-item" href="/admin/umkm?status=pending">Menunggu Verifikasi</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nama UMKM</th>
                                <th>Pemilik</th>
                                <th>Kecamatan</th>
                                <th>Tanggal Daftar</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentUmkms as $umkm)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="ms-2">
                                            <h6 class="mb-0 font-14">{{ $umkm->usaha->nama_usaha ?? 'Belum diisi' }}</h6>
                                            <p class="mb-0 font-13 text-secondary">{{ $umkm->usaha->bidang_usaha ?? 'Belum diisi' }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $umkm->nama }}</td>
                                <td>{{ $umkm->usaha->kecamatan ?? $umkm->profil->kecamatan ?? 'Belum diisi' }}</td>
                                <td>{{ $umkm->created_at->format('d M Y') }}</td>
                                <td>
                                    @php
                                        $status = $umkm->usaha->status_verifikasi ?? 'pending';
                                        $badgeClass = match($status) {
                                            'aktif' => 'bg-success',
                                            'pending' => 'bg-warning',
                                            'ditolak' => 'bg-danger',
                                            default => 'bg-warning'
                                        };
                                        $statusText = match($status) {
                                            'aktif' => 'Aktif',
                                            'pending' => 'Pending',
                                            'ditolak' => 'Ditolak',
                                            default => 'Pending'
                                        };
                                    @endphp
                                    <span class="badge {{ $badgeClass }}">{{ $statusText }}</span>
                                </td>
                                <td>
                                    <div class="d-flex order-actions">
                                        <a href="{{ route('admin.umkm.show', $umkm->id) }}" class="text-primary"><i class="bx bxs-edit"></i></a>
                                        <a href="javascript:;" class="ms-3 text-danger"><i class="bx bxs-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center">Belum ada pendaftaran UMKM</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4">
        <div class="card radius-10">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <div>
                        <h6 class="mb-0">Statistik Bulanan</h6>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container-2">
                    <canvas id="chart2"></canvas>
                </div>
            </div>
            <ul class="list-group list-group-flush">
                <li class="list-group-item d-flex bg-transparent justify-content-between align-items-center">
                    Pendaftaran Bulan Ini <span class="badge bg-success rounded-pill">{{ $totalUmkm }}</span>
                </li>
                <li class="list-group-item d-flex bg-transparent justify-content-between align-items-center">
                    Verifikasi Pending <span class="badge bg-warning rounded-pill">{{ $umkmPending }}</span>
                </li>
                <li class="list-group-item d-flex bg-transparent justify-content-between align-items-center">
                    Terverifikasi <span class="badge bg-primary rounded-pill">{{ $umkmVerified }}</span>
                </li>
            </ul>
        </div>
    </div>
</div>
<!--end row-->

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card radius-10">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <div>
                        <h6 class="mb-0">Aksi Cepat</h6>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="/admin/umkm" class="btn btn-outline-primary w-100">
                            <i class="bi bi-shop"></i><br>
                            Kelola UMKM
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/berita" class="btn btn-outline-success w-100">
                            <i class="bi bi-newspaper"></i><br>
                            Kelola Berita
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/pelatihan" class="btn btn-outline-info w-100">
                            <i class="bi bi-book"></i><br>
                            Kelola Pelatihan
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/laporan" class="btn btn-outline-warning w-100">
                            <i class="bi bi-bar-chart"></i><br>
                            Lihat Laporan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end row-->
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/chartjs/js/Chart.min.js') }}"></script>
<script>
// Chart for monthly statistics
var ctx2 = document.getElementById("chart2").getContext('2d');
var myChart2 = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ["Terverifikasi", "Pending", "Ditolak"],
        datasets: [{
            backgroundColor: [
                "#1cc88a",
                "#f6c23e", 
                "#e74a3b"
            ],
            data: [{{ $umkmVerified }}, {{ $umkmPending }}, 0]
        }]
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            }
        }
    }
});
</script>
@endpush
