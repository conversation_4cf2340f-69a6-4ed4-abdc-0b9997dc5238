<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pelatihan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\PelatihanBaruMail;

class PelatihanController extends Controller
{
    /**
     * Display a listing of pelatihans.
     */
    public function index()
    {
        $pelatihans = Pelatihan::orderBy('created_at', 'desc')->paginate(10);

        return view('admin.pelatihan.index', compact('pelatihans'));
    }

    /**
     * Show the form for creating a new pelatihan.
     */
    public function create()
    {
        return view('admin.pelatihan.create');
    }

    /**
     * Store a newly created pelatihan in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_pelatihan' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'instruktur' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required',
            'kuota' => 'required|integer|min:1',
            'biaya' => 'required|numeric|min:0',
            'kategori' => 'nullable|string|max:255',
            'tipe' => 'required|in:offline,online,hybrid',
            'lokasi' => 'required_if:tipe,offline,hybrid|string|max:255',
            'link_online' => 'required_if:tipe,online,hybrid|url',
            'status' => 'required|in:draft,open,closed,completed,cancelled',
            'syarat_peserta' => 'nullable|string',
            'materi' => 'nullable|string',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->except(['gambar']);

            // Handle image upload
            if ($request->hasFile('gambar')) {
                $image = $request->file('gambar');
                $filename = 'pelatihan_' . time() . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('pelatihan', $filename, 'public');
                $data['gambar'] = $path;
            }

            $pelatihan = Pelatihan::create($data);

            // Send notifications if status is open
            if ($request->status === 'open') {
                $this->sendNotifications($pelatihan);
            }

            DB::commit();

            return redirect()->route('admin.pelatihan.index')
                ->with('success', 'Pelatihan berhasil ditambahkan');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified pelatihan.
     */
    public function show(Pelatihan $pelatihan)
    {
        $pelatihan->load('users');

        return view('admin.pelatihan.show', compact('pelatihan'));
    }

    /**
     * Show the form for editing the specified pelatihan.
     */
    public function edit(Pelatihan $pelatihan)
    {
        return view('admin.pelatihan.edit', compact('pelatihan'));
    }

    /**
     * Update the specified pelatihan in storage.
     */
    public function update(Request $request, Pelatihan $pelatihan)
    {
        $request->validate([
            'nama_pelatihan' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'instruktur' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required',
            'kuota' => 'required|integer|min:1',
            'biaya' => 'required|numeric|min:0',
            'kategori' => 'nullable|string|max:255',
            'tipe' => 'required|in:offline,online,hybrid',
            'lokasi' => 'required_if:tipe,offline,hybrid|string|max:255',
            'link_online' => 'required_if:tipe,online,hybrid|url',
            'status' => 'required|in:draft,open,closed,completed,cancelled',
            'syarat_peserta' => 'nullable|string',
            'materi' => 'nullable|string',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->except(['gambar']);
            $oldStatus = $pelatihan->status;

            // Handle image upload
            if ($request->hasFile('gambar')) {
                // Delete old image
                if ($pelatihan->gambar) {
                    Storage::disk('public')->delete($pelatihan->gambar);
                }

                $image = $request->file('gambar');
                $filename = 'pelatihan_' . time() . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('pelatihan', $filename, 'public');
                $data['gambar'] = $path;
            }

            $pelatihan->update($data);

            // Send notifications if status changed to open and wasn't open before
            if ($request->status === 'open' && $oldStatus !== 'open') {
                $this->sendNotifications($pelatihan);
            }

            DB::commit();

            return redirect()->route('admin.pelatihan.index')
                ->with('success', 'Pelatihan berhasil diperbarui');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified pelatihan from storage.
     */
    public function destroy(Pelatihan $pelatihan)
    {
        try {
            // Delete image if exists
            if ($pelatihan->gambar) {
                Storage::disk('public')->delete($pelatihan->gambar);
            }

            $pelatihan->delete();

            return redirect()->route('admin.pelatihan.index')
                ->with('success', 'Pelatihan berhasil dihapus');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Send notifications to all UMKM users
     */
    private function sendNotifications(Pelatihan $pelatihan)
    {
        $umkmUsers = User::where('role', 'umkm')->get();
        $notificationCount = 0;

        foreach ($umkmUsers as $user) {
            try {
                // Send email notification
                Mail::to($user->email)->send(new PelatihanBaruMail($pelatihan, $user));

                // Create dashboard notification (you can implement this later)
                // NotifikasiUmkm::create([...]);

                $notificationCount++;
            } catch (\Exception $e) {
                // Log error but continue with other notifications
                \Log::error('Failed to send notification to user ' . $user->id . ': ' . $e->getMessage());
            }
        }

        // Update pelatihan with notification info
        $pelatihan->update([
            'notifikasi_terkirim' => $notificationCount,
            'notifikasi_dikirim_at' => now(),
            'email_terkirim' => true
        ]);

        return $notificationCount;
    }

    /**
     * Show notification history
     */
    public function notificationHistory()
    {
        $pelatihans = Pelatihan::whereNotNull('notifikasi_dikirim_at')
            ->orderBy('notifikasi_dikirim_at', 'desc')
            ->paginate(10);

        return view('admin.pelatihan.notification-history', compact('pelatihans'));
    }
}
