<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Hapus data duplikat berdasarkan NIK sebelum menambahkan unique constraint
        DB::statement('
            DELETE FROM profils
            WHERE id NOT IN (
                SELECT * FROM (
                    SELECT MIN(id)
                    FROM profils
                    GROUP BY nik
                ) AS temp
            )
        ');

        // Cek apakah unique constraint sudah ada
        $indexExists = DB::select("SELECT name FROM sqlite_master WHERE type='index' AND name='profils_nik_unique'");
        if (empty($indexExists)) {
            // Tambahkan unique constraint jika belum ada
            Schema::table('profils', function (Blueprint $table) {
                $table->unique('nik');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('profils', function (Blueprint $table) {
            $table->dropUnique(['nik']);
        });
    }
};
