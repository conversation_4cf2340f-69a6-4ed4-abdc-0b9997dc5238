<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Wilayah API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Wilayah API</h1>
    
    <div>
        <h3>Test Provinces API</h3>
        <button onclick="testProvinces()">Test Provinces</button>
        <div id="provinces-result"></div>
    </div>
    
    <div>
        <h3>Test Regencies API</h3>
        <button onclick="testRegencies()">Test Regencies (Jawa Tengah)</button>
        <div id="regencies-result"></div>
    </div>

    <script>
        function testProvinces() {
            $.get('/api/wilayah/provinces')
                .done(function(data) {
                    console.log('Provinces data:', data);
                    $('#provinces-result').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                })
                .fail(function(xhr, status, error) {
                    console.error('Error:', error);
                    $('#provinces-result').html('<p style="color: red;">Error: ' + error + '</p>');
                });
        }
        
        function testRegencies() {
            // Test dengan kode Jawa Tengah (33)
            $.get('/api/wilayah/regencies/33')
                .done(function(data) {
                    console.log('Regencies data:', data);
                    $('#regencies-result').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                })
                .fail(function(xhr, status, error) {
                    console.error('Error:', error);
                    $('#regencies-result').html('<p style="color: red;">Error: ' + error + '</p>');
                });
        }
    </script>
</body>
</html>
