{{-- DataTables Component --}}
<div class="table-container">
    @if(isset($title) || isset($description))
    <div class="table-header">
        <div>
            @if(isset($title))
                <h3 class="table-title">{{ $title }}</h3>
            @endif
            @if(isset($description))
                <p class="table-description">{{ $description }}</p>
            @endif
        </div>
        @if(isset($actions))
            <div class="table-actions">
                {{ $actions }}
            </div>
        @endif
    </div>
    @endif

    @if(isset($filters))
    <div class="table-filters">
        {{ $filters }}
    </div>
    @endif

    <div class="table-wrapper">
        <table id="{{ $tableId ?? 'dataTable' }}" class="table table-striped table-bordered dt-responsive nowrap" style="width:100%">
            @if(isset($headers))
            <thead>
                <tr>
                    {{ $headers }}
                </tr>
            </thead>
            @endif
            <tbody>
                {{ $slot }}
            </tbody>
        </table>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('#{{ $tableId ?? 'dataTable' }}').DataTable({
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        dom: 'Bfrtip',
        buttons: [
            {
                text: '<i class="fas fa-download"></i> Export CSV',
                className: 'btn btn-primary btn-sm',
                action: function (e, dt, node, config) {
                    window.location.href = '/admin/umkm/export';
                }
            }
        ],
        language: {
            "sProcessing": "Sedang memproses...",
            "sLengthMenu": "Tampilkan _MENU_ entri",
            "sZeroRecords": "Tidak ditemukan data yang sesuai",
            "sInfo": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
            "sInfoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
            "sInfoFiltered": "(disaring dari _MAX_ entri keseluruhan)",
            "sInfoPostFix": "",
            "sSearch": "Cari:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "Pertama",
                "sPrevious": "Sebelumnya",
                "sNext": "Selanjutnya",
                "sLast": "Terakhir"
            }
        },
        columnDefs: [
            {
                targets: -1, // Last column (Actions)
                orderable: false,
                searchable: false
            }
        ]
    });
});
</script>
@endpush
