<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WilayahController;
use App\Http\Controllers\Auth\{
    AuthController,
    UmkmRegistrationController
};
use App\Http\Controllers\Landingpage\LandingpageController;
use App\Http\Controllers\Admin\{
    DashboardController,
    UmkmController as AdminUmkmController,
    UserController as AdminUserController,
    BeritaController,
    PelatihanController as Admin<PERSON>elatihanController,
    GaleriController,
    LaporanController,
    PengaturanController
};
use App\Http\Controllers\Umkm\UmkmDashboardController;

// ------------------------
// LANDING PAGE ROUTES (Updated)
// ------------------------
Route::prefix('/')->name('landingpage.')->group(function () {
    Route::redirect('/', '/beranda');
    Route::get('/beranda', [LandingpageController::class, 'beranda'])->name('beranda');
    Route::get('/profil', [LandingpageController::class, 'profil'])->name('profil');
    Route::get('/berita', [LandingpageController::class, 'berita'])->name('berita');
    
    // Updated Pelatihan Routes
    Route::get('/pelatihan', [LandingpageController::class, 'pelatihan'])->name('pelatihan');
    Route::get('/pelatihan/{pelatihan}', [LandingpageController::class, 'pelatihanShow'])->name('pelatihan.detail');
    
    Route::get('/galeri', [LandingpageController::class, 'galeri'])->name('galeri');
    Route::get('/peta', [LandingpageController::class, 'peta'])->name('peta');
    Route::get('/kontak', [LandingpageController::class, 'kontak'])->name('kontak');
});

//
// ------------------------
// AUTH ROUTES
// ------------------------
Route::prefix('auth')->name('auth.')->group(function () {
    // Login
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.store');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Register
    Route::get('/register', [UmkmRegistrationController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [UmkmRegistrationController::class, 'register'])->name('register.store');

    // AJAX Validation
    Route::post('/check-nik', [UmkmRegistrationController::class, 'checkNik'])->name('check-nik');
    Route::post('/check-email', [UmkmRegistrationController::class, 'checkEmail'])->name('check-email');

    // Password Reset
    Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('forgot-password');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('forgot-password.send');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('reset-password');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('reset-password.update');
});

//
// ------------------------
// ADMIN ROUTES
// ------------------------
Route::prefix('admin')->name('admin.')->/* TODO: Aktifkan middleware(['auth', 'admin']) */group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Konten Pages
    Route::get('/berita', [BeritaController::class, 'index'])->name('berita.index');
    Route::get('/pelatihan', [AdminPelatihanController::class, 'index'])->name('pelatihan.index');
    Route::get('/galeri', [GaleriController::class, 'index'])->name('galeri.index');
    Route::get('/laporan', [LaporanController::class, 'index'])->name('laporan.index');
    Route::get('/pengaturan', [PengaturanController::class, 'index'])->name('pengaturan.index');

    // UMKM Management
    Route::prefix('umkm')->name('umkm.')->group(function () {
        Route::get('/', [AdminUmkmController::class, 'index'])->name('index');
        Route::post('/', [AdminUmkmController::class, 'store'])->name('store');
        Route::post('/filter', [AdminUmkmController::class, 'filter'])->name('filter');
        Route::get('/export', [AdminUmkmController::class, 'export'])->name('export');
        Route::get('/export/excel', [AdminUmkmController::class, 'exportExcel'])->name('export.excel');
        Route::get('/export/pdf', [AdminUmkmController::class, 'exportPDF'])->name('export.pdf');
        Route::get('/{id}/export', [AdminUmkmController::class, 'exportUser'])->name('export.user');
        Route::post('/approve-multiple', [AdminUmkmController::class, 'approveMultiple'])->name('approve-multiple');
        Route::post('/reject-multiple', [AdminUmkmController::class, 'rejectMultiple'])->name('reject-multiple');
        Route::get('/{id}', [AdminUmkmController::class, 'show'])->name('show');
        Route::get('/{id}/detail', [AdminUmkmController::class, 'detail'])->name('detail');
        Route::get('/{id}/edit', [AdminUmkmController::class, 'edit'])->name('edit');
        Route::get('/{id}/edit-form', [AdminUmkmController::class, 'editForm'])->name('edit-form');
        Route::put('/{id}', [AdminUmkmController::class, 'update'])->name('update');
        Route::delete('/{id}', [AdminUmkmController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/approve', [AdminUmkmController::class, 'approve'])->name('approve');
        Route::post('/{id}/reject', [AdminUmkmController::class, 'reject'])->name('reject');
    });

    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [AdminUserController::class, 'index'])->name('index');
        Route::post('/filter', [AdminUserController::class, 'filter'])->name('filter');
        Route::get('/create', [AdminUserController::class, 'create'])->name('create');
        Route::post('/', [AdminUserController::class, 'store'])->name('store');
        Route::get('/{id}', [AdminUserController::class, 'show'])->name('show');
        Route::get('/{id}/info', [AdminUserController::class, 'getUserInfo'])->name('info');
        Route::get('/{id}/edit', [AdminUserController::class, 'edit'])->name('edit');
        Route::put('/{id}', [AdminUserController::class, 'update'])->name('update');
        Route::delete('/{id}', [AdminUserController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/activate', [AdminUserController::class, 'activate'])->name('activate');
        Route::post('/{id}/deactivate', [AdminUserController::class, 'deactivate'])->name('deactivate');
        Route::post('/bulk-activate', [AdminUserController::class, 'bulkActivate'])->name('bulk-activate');
    });

    // Pelatihan Management
    Route::prefix('pelatihan')->name('pelatihan.')->group(function () {
        Route::get('/', [AdminPelatihanController::class, 'index'])->name('index');
        Route::get('/create', [AdminPelatihanController::class, 'create'])->name('create');
        Route::post('/', [AdminPelatihanController::class, 'store'])->name('store');
        Route::get('/notification-history', [AdminPelatihanController::class, 'notificationHistory'])->name('notification-history');
        Route::get('/{pelatihan}', [AdminPelatihanController::class, 'show'])->name('show');
        Route::get('/{pelatihan}/edit', [AdminPelatihanController::class, 'edit'])->name('edit');
        Route::put('/{pelatihan}', [AdminPelatihanController::class, 'update'])->name('update');
        Route::delete('/{pelatihan}', [AdminPelatihanController::class, 'destroy'])->name('destroy');
    });
});

//
// ------------------------
// UMKM DASHBOARD ROUTES
// ------------------------
Route::prefix('umkm')->name('umkm.')->/* TODO: Aktifkan middleware(['auth', 'umkm']) */group(function () {
    Route::get('/dashboard', function () {
        return view('umkm.dashboard-new');
    })->name('dashboard');

    Route::get('/profil', [UmkmDashboardController::class, 'profil'])->name('profil');
    Route::get('/profil/edit', [UmkmDashboardController::class, 'editProfil'])->name('profil.edit');
    Route::put('/profil', [UmkmDashboardController::class, 'updateProfil'])->name('profil.update');
});

//
// ------------------------
// API ROUTES - WILAYAH
// ------------------------
Route::prefix('api/wilayah')->name('api.wilayah.')->group(function () {
    Route::get('/provinces', [WilayahController::class, 'getProvinces'])->name('provinces');
    Route::get('/regencies/{provinceCode}', [WilayahController::class, 'getRegencies'])->name('regencies');
    Route::get('/districts/{regencyCode}', [WilayahController::class, 'getDistricts'])->name('districts');
    Route::get('/villages/{districtCode}', [WilayahController::class, 'getVillages'])->name('villages');
});

// Test route for API
Route::get('/test-api', function () {
    return response()->json(['message' => 'API is working', 'timestamp' => now()]);
});

//
// ------------------------
// DEV/TESTING ROUTES (jangan upload ke production)
// ------------------------
if (app()->environment(['local', 'testing'])) {
    Route::get('/test-db', function () {
        try {
            return response()->json([
                'status' => 'success',
                'users' => \App\Models\User::count(),
                'profils' => \App\Models\Profil::count(),
                'usaha' => \App\Models\Usaha::count(),
                'database_path' => database_path('database.sqlite'),
                'database_exists' => file_exists(database_path('database.sqlite'))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    })->name('test.db');
}
