# Panduan Penggunaan API Wilayah Indonesia - Format Baru

## Format Response API

Berdasarkan struktur response yang diberikan, API wilayah menggunakan format standar dengan struktur:

```json
{
  "status": "success",
  "total": 34,
  "data": [
    {
      "nama_wilayah": "ACEH",
      "kode_wilayah": "11",
      "level_wilayah": "provinsi",
      "population": {
        "usia_0_4_thn": "58655",
        "usia_5_9": "69725",
        "tahun": "2021"
      }
    }
  ]
}
```

## Endpoint API

### 1. Provinsi
```javascript
// URL: https://wilayah.id/api/provinces.json
const response = await fetch('https://wilayah.id/api/provinces.json');
const result = await response.json();

// Response format:
{
  "status": "success",
  "total": 34,
  "data": [
    {
      "nama_wilayah": "ACEH",
      "kode_wilayah": "11", 
      "level_wilayah": "provinsi",
      "population": { ... }
    }
  ]
}
```

### 2. Kabupaten/Kota
```javascript
// URL: https://wilayah.id/api/regencies/{kode_provinsi}.json
const response = await fetch('https://wilayah.id/api/regencies/11.json');
const result = await response.json();

// Response format:
{
  "status": "success", 
  "total": 23,
  "data": [
    {
      "nama_wilayah": "KAB. ACEH SELATAN",
      "kode_wilayah": "11.01",
      "level_wilayah": "kabupaten_kota",
      "kode_provinsi": "11",
      "demografi": { ... }
    }
  ]
}
```

### 3. Kecamatan
```javascript
// URL: https://wilayah.id/api/districts/{kode_kabupaten}.json
const response = await fetch('https://wilayah.id/api/districts/11.01.json');
const result = await response.json();

// Response format:
{
  "status": "success",
  "total": 10, 
  "data": [
    {
      "nama_wilayah": "Bakongan",
      "kode_wilayah": "11.01.01",
      "level_wilayah": "kecamatan",
      "kode_kabupaten_kota": "11.01",
      "kode_provinsi": "11"
    }
  ]
}
```

### 4. Desa/Kelurahan
```javascript
// URL: https://wilayah.id/api/villages/{kode_kecamatan}.json
const response = await fetch('https://wilayah.id/api/villages/11.01.01.json');
const result = await response.json();

// Response format:
{
  "status": "success",
  "total": 15,
  "data": [
    {
      "nama_wilayah": "Gampong A", 
      "kode_wilayah": "***********",
      "level_wilayah": "desa_kelurahan",
      "kode_kecamatan": "11.01.01",
      "kode_kabupaten_kota": "11.01",
      "kode_provinsi": "11"
    }
  ]
}
```

## Implementasi JavaScript

### Fungsi Fetch Data
```javascript
async function fetchWilayahData(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Network response was not ok');
        const result = await response.json();
        
        // Handle new API response format
        if (result.status === 'success' && result.data) {
            return result.data;
        }
        return [];
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}
```

### Load Provinsi
```javascript
async function loadProvinces() {
    const data = await fetchWilayahData('https://wilayah.id/api/provinces.json');
    
    const select = document.getElementById('provinsi_usaha');
    select.innerHTML = '<option value="">Pilih Provinsi</option>';
    
    data.forEach(province => {
        const option = document.createElement('option');
        option.value = province.kode_wilayah; // Gunakan kode_wilayah
        option.textContent = province.nama_wilayah; // Gunakan nama_wilayah
        select.appendChild(option);
    });
}
```

### Load Kabupaten/Kota
```javascript
async function loadRegencies(provinceCode) {
    const data = await fetchWilayahData(`https://wilayah.id/api/regencies/${provinceCode}.json`);
    
    const select = document.getElementById('kabupaten_usaha');
    select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
    
    data.forEach(regency => {
        const option = document.createElement('option');
        option.value = regency.kode_wilayah;
        option.textContent = regency.nama_wilayah;
        select.appendChild(option);
    });
}
```

### Load Kecamatan
```javascript
async function loadDistricts(regencyCode) {
    const data = await fetchWilayahData(`https://wilayah.id/api/districts/${regencyCode}.json`);
    
    const select = document.getElementById('kecamatan_usaha');
    select.innerHTML = '<option value="">Pilih Kecamatan</option>';
    
    data.forEach(district => {
        const option = document.createElement('option');
        option.value = district.kode_wilayah;
        option.textContent = district.nama_wilayah;
        select.appendChild(option);
    });
}
```

### Load Desa/Kelurahan
```javascript
async function loadVillages(districtCode) {
    const data = await fetchWilayahData(`https://wilayah.id/api/villages/${districtCode}.json`);
    
    const select = document.getElementById('desa_usaha');
    select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    
    data.forEach(village => {
        const option = document.createElement('option');
        option.value = village.kode_wilayah;
        option.textContent = village.nama_wilayah;
        select.appendChild(option);
    });
}
```

## Contoh Penggunaan Lengkap

```javascript
// Event listener untuk dropdown cascade
document.getElementById('provinsi_usaha').addEventListener('change', function() {
    if (this.value) {
        loadRegencies(this.value);
    }
});

document.getElementById('kabupaten_usaha').addEventListener('change', function() {
    if (this.value) {
        loadDistricts(this.value);
    }
});

document.getElementById('kecamatan_usaha').addEventListener('change', function() {
    if (this.value) {
        loadVillages(this.value);
    }
});

// Load data awal
document.addEventListener('DOMContentLoaded', function() {
    loadProvinces();
});
```

## Perbedaan dengan Format Lama

| Format Lama | Format Baru |
|-------------|-------------|
| `province.code` | `province.kode_wilayah` |
| `province.name` | `province.nama_wilayah` |
| Response langsung array | Response dalam `result.data` |
| Tidak ada status | Ada `result.status` |

## Testing

Untuk test API, buka browser console dan jalankan:

```javascript
// Test load provinsi
fetch('https://wilayah.id/api/provinces.json')
  .then(response => response.json())
  .then(data => console.log(data));

// Test load kabupaten Jawa Tengah (33)
fetch('https://wilayah.id/api/regencies/33.json')
  .then(response => response.json())
  .then(data => console.log(data));

// Test load kecamatan Purworejo (33.06)
fetch('https://wilayah.id/api/districts/33.06.json')
  .then(response => response.json())
  .then(data => console.log(data));
```

## Catatan Penting

1. **Field Names**: Gunakan `kode_wilayah` dan `nama_wilayah` bukan `code` dan `name`
2. **Response Structure**: Data ada di dalam `result.data`, bukan langsung array
3. **Status Check**: Selalu cek `result.status === 'success'` sebelum menggunakan data
4. **Error Handling**: Implementasikan error handling yang robust
5. **Loading State**: Berikan feedback loading saat fetch data
