<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class WilayahController extends Controller
{
    private $baseUrl = 'https://wilayah.id/api';

    /**
     * Get all provinces
     */
    public function getProvinces()
    {
        try {
            // Cache untuk 24 jam
            $provinces = Cache::remember('provinces', 24 * 60 * 60, function () {
                $response = Http::timeout(30)->get($this->baseUrl . '/provinces.json');

                if ($response->successful()) {
                    return $response->json();
                }

                return null;
            });

            if ($provinces) {
                return response()->json($provinces);
            }

            return response()->json([
                'data' => [],
                'meta' => ['error' => 'Failed to fetch provinces']
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'data' => [],
                'meta' => ['error' => $e->getMessage()]
            ], 500);
        }
    }

    /**
     * Get regencies by province code
     */
    public function getRegencies($provinceCode)
    {
        try {
            $cacheKey = "regencies_{$provinceCode}";

            $regencies = Cache::remember($cacheKey, 24 * 60 * 60, function () use ($provinceCode) {
                $response = Http::timeout(30)->get($this->baseUrl . "/regencies/{$provinceCode}.json");

                if ($response->successful()) {
                    return $response->json();
                }

                return null;
            });

            if ($regencies) {
                return response()->json($regencies);
            }

            return response()->json([
                'data' => [],
                'meta' => ['error' => 'Failed to fetch regencies']
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'data' => [],
                'meta' => ['error' => $e->getMessage()]
            ], 500);
        }
    }

    /**
     * Get districts by regency code
     */
    public function getDistricts($regencyCode)
    {
        try {
            $cacheKey = "districts_{$regencyCode}";

            $districts = Cache::remember($cacheKey, 24 * 60 * 60, function () use ($regencyCode) {
                $response = Http::timeout(30)->get($this->baseUrl . "/districts/{$regencyCode}.json");

                if ($response->successful()) {
                    return $response->json();
                }

                return null;
            });

            if ($districts) {
                return response()->json($districts);
            }

            // Fallback untuk Purworejo jika API gagal
            if ($regencyCode === '33.06') {
                return response()->json([
                    'data' => [
                        ['code' => '33.06.01', 'name' => 'Bagelen'],
                        ['code' => '33.06.02', 'name' => 'Banyuurip'],
                        ['code' => '33.06.03', 'name' => 'Bayan'],
                        ['code' => '33.06.04', 'name' => 'Bener'],
                        ['code' => '33.06.05', 'name' => 'Bruno'],
                        ['code' => '33.06.06', 'name' => 'Gebang'],
                        ['code' => '33.06.07', 'name' => 'Grabag'],
                        ['code' => '33.06.08', 'name' => 'Kaligesing'],
                        ['code' => '33.06.09', 'name' => 'Kemiri'],
                        ['code' => '33.06.10', 'name' => 'Kutoarjo'],
                        ['code' => '33.06.11', 'name' => 'Loano'],
                        ['code' => '33.06.12', 'name' => 'Ngombol'],
                        ['code' => '33.06.13', 'name' => 'Pituruh'],
                        ['code' => '33.06.14', 'name' => 'Purwodadi'],
                        ['code' => '33.06.15', 'name' => 'Purworejo'],
                        ['code' => '33.06.16', 'name' => 'Rembang']
                    ],
                    'meta' => ['fallback' => true]
                ]);
            }

            return response()->json([
                'data' => [],
                'meta' => ['error' => 'Failed to fetch districts']
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'data' => [],
                'meta' => ['error' => $e->getMessage()]
            ], 500);
        }
    }

    /**
     * Get villages by district code
     */
    public function getVillages($districtCode)
    {
        try {
            $cacheKey = "villages_{$districtCode}";

            $villages = Cache::remember($cacheKey, 24 * 60 * 60, function () use ($districtCode) {
                $response = Http::timeout(30)->get($this->baseUrl . "/villages/{$districtCode}.json");

                if ($response->successful()) {
                    return $response->json();
                }

                return null;
            });

            if ($villages) {
                return response()->json($villages);
            }

            return response()->json([
                'data' => [],
                'meta' => ['error' => 'Failed to fetch villages']
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'data' => [],
                'meta' => ['error' => $e->getMessage()]
            ], 500);
        }
    }
}
