# Dokumentasi Halaman Admin UMKM

## Overview
Halaman admin UMKM telah dibuat dengan menggunakan template LTR dari `public/ltr` dan terhubung dengan database serta API wilayah Indonesia. Halaman ini dapat diakses di `http://127.0.0.1:8000/admin/umkm`.

## Fitur yang Tersedia

### 1. Dashboard Widget Statistik
- **Total UMKM**: Menampilkan jumlah total UMKM yang terdaftar
- **UMKM Terverifikasi**: Menampilkan jumlah UMKM dengan status verified
- **UMKM Pending**: Menampilkan jumlah UMKM dengan status pending
- **UMKM Bulan Ini**: Menampilkan jumlah UMKM yang mendaftar bulan ini

### 2. Data Table UMKM
- Menampilkan data UMKM dalam format tabel dengan fitur:
  - Pencarian (search)
  - Pagination
  - Sorting
  - Responsive design
- **Kolom Legalitas**: Menampilkan badge legalitas yang dimiliki UMKM
  - Maksimal 2 badge ditampilkan
  - Jika lebih dari 2, di<PERSON><PERSON><PERSON><PERSON> "+<PERSON> lainnya"
  - Badge dengan warna berbeda untuk setiap jenis legalitas

### 3. Aksi CRUD dengan Modal
#### A. Lihat Detail (View) - Modal XL
- Tombol mata (👁️) untuk melihat detail UMKM
- Modal popup ukuran XL menampilkan informasi lengkap:
  - Informasi Usaha
  - Informasi Pemilik
  - Alamat Usaha
  - Legalitas & Dokumen (lengkap dengan nomor)
  - Informasi Tambahan
- **Aksi dari Modal Detail**:
  - Tombol **Edit** - Membuka modal edit
  - Tombol **Hapus** - Konfirmasi hapus
  - Tombol **Setujui** - Approve UMKM (status menjadi verified)
  - Tombol **Tolak** - Reject UMKM (status menjadi rejected)

#### B. Edit UMKM - Modal XL
- Tombol pensil (✏️) untuk edit data UMKM
- Form modal dengan field lengkap:
  - Informasi Pribadi (nama, email, no HP, NIK, jenis kelamin, password)
  - Alamat Pribadi (provinsi, kabupaten, kecamatan, desa, alamat lengkap)
  - Informasi Usaha (nama usaha, merk, bidang usaha, deskripsi)
  - Alamat Usaha (provinsi, kabupaten, kecamatan, desa, alamat lengkap)
  - **Legalitas & Dokumen**:
    - Checkbox untuk NIB, SIUP, PIRT, HKI
    - Input nomor untuk setiap jenis legalitas
    - Form dinamis untuk legalitas lainnya
    - Tombol tambah/hapus legalitas custom

#### C. Hapus UMKM
- Tombol trash (🗑️) untuk menghapus UMKM
- Konfirmasi dengan SweetAlert2
- Menghapus data user beserta relasi (profil, usaha, legalitas)

#### D. Tambah UMKM - Modal XL
- Tombol "Tambah UMKM" di header
- Form modal yang sama dengan edit
- Password wajib diisi untuk UMKM baru
- Semua field legalitas tersedia

#### E. Approve/Reject UMKM
- **Approve**: Mengubah status menjadi "verified"
- **Reject**: Mengubah status menjadi "rejected"
- Konfirmasi dengan SweetAlert2
- Update real-time di tabel

### 6. Halaman Detail UMKM dengan Verifikasi
#### A. URL Akses
- Dapat diakses dari tombol "Lihat Detail" di tabel UMKM
- URL: `/admin/umkm/{id}`

#### B. Fitur Verifikasi di Halaman Detail
- **Tombol Verifikasi di Header**:
  - Tombol "Setujui" dan "Tolak" di breadcrumb
  - Hanya muncul jika status UMKM = pending
- **Card Verifikasi Khusus**:
  - Card berwarna sesuai status (kuning=pending, hijau=verified, merah=rejected)
  - Tombol aksi verifikasi di dalam card
  - Informasi tanggal dan catatan verifikasi

#### C. Status Verifikasi
1. **Pending (Menunggu Verifikasi)**:
   - Card kuning dengan peringatan
   - Tombol "Setujui" dan "Tolak" tersedia
   - Pesan: "UMKM ini memerlukan verifikasi admin"

2. **Verified (Sudah Terverifikasi)**:
   - Card hijau dengan ikon centang
   - Menampilkan tanggal verifikasi
   - Pesan: "UMKM telah diverifikasi dan dapat mengakses semua fitur"

3. **Rejected (Ditolak)**:
   - Card merah dengan ikon X
   - Menampilkan alasan penolakan
   - Tombol "Setujui" untuk re-verifikasi

#### D. Proses Verifikasi
1. **Approve UMKM**:
   - Klik tombol "Setujui"
   - Konfirmasi dengan SweetAlert2
   - Status berubah ke "verified"
   - Menyimpan tanggal dan admin yang memverifikasi

2. **Reject UMKM**:
   - Klik tombol "Tolak"
   - Modal input alasan penolakan (wajib diisi)
   - Status berubah ke "rejected"
   - Menyimpan alasan, tanggal, dan admin yang menolak

#### E. Data yang Disimpan
- **verification_status**: pending/verified/rejected
- **verified_at**: Tanggal verifikasi/penolakan
- **verified_by**: ID admin yang melakukan verifikasi
- **verification_notes**: Catatan/alasan verifikasi

### 7. Fitur Checkbox dan Verifikasi Massal
#### A. Checkbox Selection
- **Checkbox Header**: Select/deselect semua UMKM yang terlihat
- **Checkbox Row**: Select/deselect UMKM individual
- **Indeterminate State**: Checkbox header menunjukkan status sebagian terpilih
- **Auto-update**: Checkbox header otomatis update berdasarkan selection

#### B. Bulk Actions Card
- **Conditional Display**: Muncul hanya ketika ada UMKM yang dipilih
- **Counter**: Menampilkan jumlah UMKM yang dipilih
- **Actions Available**:
  - **Setujui Terpilih**: Approve multiple UMKM sekaligus
  - **Tolak Terpilih**: Reject multiple UMKM dengan alasan
  - **Batal**: Clear semua selection

#### C. Bulk Approve Process
1. Pilih UMKM dengan checkbox
2. Klik "Setujui Terpilih"
3. Konfirmasi dengan SweetAlert2
4. Semua UMKM terpilih status berubah ke "verified"
5. Menyimpan data verifikasi (tanggal, admin, catatan)

#### D. Bulk Reject Process
1. Pilih UMKM dengan checkbox
2. Klik "Tolak Terpilih"
3. Input alasan penolakan (wajib)
4. Konfirmasi dengan SweetAlert2
5. Semua UMKM terpilih status berubah ke "rejected"
6. Menyimpan alasan penolakan

### 8. Sistem Filter Lanjutan
#### A. Filter Options
1. **Status Verifikasi**:
   - Semua Status
   - Menunggu Verifikasi (pending)
   - Terverifikasi (verified)
   - Ditolak (rejected)

2. **Bidang Usaha**:
   - Semua Bidang
   - Makanan & Minuman
   - Kerajinan Tangan
   - Perdagangan
   - Jasa

3. **Kecamatan**:
   - Semua Kecamatan
   - Dropdown dinamis berdasarkan data UMKM
   - Auto-populate dari database

4. **Pencarian**:
   - Search box untuk nama UMKM atau pemilik
   - Real-time search dengan Enter key
   - Case-insensitive search

#### B. Filter Actions
- **Terapkan Filter**: Apply semua filter yang dipilih
- **Reset**: Clear semua filter dan tampilkan semua data
- **Auto-hide**: Baris yang tidak sesuai filter disembunyikan
- **Checkbox Update**: Selection otomatis clear untuk baris tersembunyi

#### C. Filter Feedback
- **Result Counter**: Notifikasi jumlah hasil filter
- **Visual Feedback**: SweetAlert2 untuk konfirmasi filter
- **Persistent State**: Filter tetap aktif sampai direset

### 9. Integrasi dengan DataTable
#### A. Column Configuration
- **Checkbox Column**: Non-sortable, width fixed 50px
- **Action Column**: Non-sortable untuk konsistensi
- **Default Sort**: Berdasarkan nama UMKM (kolom ke-2)
- **Responsive**: Optimal di semua device

#### B. Enhanced Features
- **Row Data Attributes**: Status, bidang, kecamatan untuk filtering
- **Dynamic Visibility**: Show/hide berdasarkan filter
- **Selection Persistence**: Checkbox state maintained during filter
- **Pagination**: Bekerja dengan filter dan selection

### 10. User Experience Improvements
#### A. Visual Indicators
- **Bulk Actions Card**: Muncul dengan animasi smooth
- **Selection Counter**: Real-time update jumlah terpilih
- **Filter Status**: Notifikasi hasil filter
- **Loading States**: Feedback visual untuk AJAX requests

#### B. Keyboard Support
- **Enter Key**: Trigger search dari input box
- **Escape Key**: Clear selection (future enhancement)
- **Tab Navigation**: Accessible form navigation

#### C. Mobile Responsiveness
- **Responsive Table**: Optimal di mobile device
- **Touch-friendly**: Checkbox dan button mudah diakses
- **Compact Layout**: Filter tersusun rapi di mobile

### 4. Integrasi API Wilayah
Sistem terintegrasi dengan API wilayah Indonesia (`https://wilayah.id/api`) untuk:
- **Provinsi**: Dropdown provinsi dari API
- **Kabupaten**: Dropdown kabupaten berdasarkan provinsi yang dipilih
- **Kecamatan**: Dropdown kecamatan berdasarkan kabupaten yang dipilih
- **Desa**: Dropdown desa berdasarkan kecamatan yang dipilih

API endpoints yang digunakan:
- `/api/wilayah/provinces` - Mendapatkan daftar provinsi
- `/api/wilayah/regencies/{provinceCode}` - Mendapatkan kabupaten berdasarkan kode provinsi
- `/api/wilayah/districts/{regencyCode}` - Mendapatkan kecamatan berdasarkan kode kabupaten
- `/api/wilayah/villages/{districtCode}` - Mendapatkan desa berdasarkan kode kecamatan

### 5. Template dan Styling
- **Layout**: Menggunakan `layouts/admin-ltr.blade.php` (layout standar admin)
- **Sidebar**: Menu navigasi lengkap dengan aksesibilitas
- **Theme Switcher**: Fitur ganti tema (light/dark/semi-dark)
- **Header**: Notifikasi, pencarian, dan user dropdown
- **Template LTR**: Assets dari `public/ltr/assets/`
- **Bootstrap 5**: Framework CSS modern
- **Bootstrap Icons**: Icon library
- **DataTables**: Tabel interaktif
- **SweetAlert2**: Notifikasi dan konfirmasi

## File yang Dibuat/Dimodifikasi

### 1. View
- `resources/views/admin/umkm/index-clean.blade.php` - Halaman utama admin UMKM (extends admin-ltr layout)
- `resources/views/layouts/admin-ltr.blade.php` - Layout admin dengan sidebar dan fitur aksesibilitas

### 2. Controller
- `app/Http/Controllers/Admin/UmkmController.php`:
  - Method `indexLtr()` - Menampilkan halaman dengan template LTR
  - Method `edit()` - Mengembalikan data UMKM untuk form edit (JSON)
  - Method `store()` - Menyimpan UMKM baru dengan integrasi API wilayah
  - Method `update()` - Update data UMKM dengan integrasi API wilayah
  - Method `destroy()` - Menghapus UMKM dan relasi

### 3. Routes
- `routes/web.php` - Route admin UMKM menggunakan method `indexLtr()`

## Teknologi yang Digunakan

### Frontend
- **HTML5 & CSS3**
- **Bootstrap 5** - Framework CSS
- **jQuery** - JavaScript library
- **DataTables** - Plugin tabel interaktif
- **SweetAlert2** - Library notifikasi
- **Bootstrap Icons** - Icon library

### Backend
- **Laravel 10** - PHP Framework
- **MySQL/SQLite** - Database
- **Eloquent ORM** - Database abstraction

### API Integration
- **Wilayah Indonesia API** - API wilayah administratif Indonesia
- **HTTP Client Laravel** - Untuk konsumsi API

## Cara Penggunaan

### 1. Akses Halaman
Buka browser dan akses: `http://127.0.0.1:8000/admin/umkm`

### 2. Melihat Statistik
Statistik UMKM ditampilkan dalam bentuk widget di bagian atas halaman.

### 3. Mengelola Data UMKM
- **Tambah**: Klik tombol "Tambah UMKM" di header
- **Lihat Detail**: Klik tombol mata pada kolom aksi
- **Edit**: Klik tombol pensil pada kolom aksi
- **Hapus**: Klik tombol trash pada kolom aksi

### 4. Menggunakan Form
- Pilih provinsi terlebih dahulu, kemudian kabupaten, kecamatan, dan desa akan terisi otomatis
- Untuk alamat usaha, lakukan hal yang sama
- Password wajib diisi untuk UMKM baru, optional untuk edit

### 5. Export Data
Klik tombol "Export CSV" untuk mengunduh data UMKM dalam format CSV.

## Catatan Penting

1. **API Wilayah**: Pastikan koneksi internet tersedia untuk mengakses API wilayah
2. **Database**: Pastikan database sudah dikonfigurasi dengan benar
3. **Assets**: Template LTR harus tersedia di `public/ltr/`
4. **Permissions**: Pastikan user memiliki akses admin untuk menggunakan fitur ini

## Troubleshooting

### 1. API Wilayah Tidak Berfungsi
- Periksa koneksi internet
- Cek apakah API `https://wilayah.id/api` dapat diakses
- Lihat console browser untuk error JavaScript

### 2. Form Tidak Menyimpan
- Periksa validasi form
- Cek console browser untuk error AJAX
- Pastikan CSRF token tersedia

### 3. Template Tidak Muncul
- Pastikan file assets LTR tersedia di `public/ltr/`
- Periksa path asset di view
- Jalankan `php artisan storage:link` jika diperlukan
