<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test WilayahController <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Test WilayahController <PERSON></h1>
        
        <!-- Test Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <button onclick="testProvinces()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Test Provinsi
            </button>
            <button onclick="testRegencies()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                Test Kabupaten (Jawa Tengah)
            </button>
            <button onclick="testDistricts()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                Test Kecamatan (Purworejo)
            </button>
            <button onclick="testVillages()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                Test Desa (Bagelen)
            </button>
        </div>

        <!-- API Endpoints Info -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">Laravel API Endpoints</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>Provinsi:</strong><br>
                    <code class="bg-gray-100 p-1 rounded">GET /api/wilayah/provinces</code>
                </div>
                <div>
                    <strong>Kabupaten:</strong><br>
                    <code class="bg-gray-100 p-1 rounded">GET /api/wilayah/regencies/{provinceCode}</code>
                </div>
                <div>
                    <strong>Kecamatan:</strong><br>
                    <code class="bg-gray-100 p-1 rounded">GET /api/wilayah/districts/{regencyCode}</code>
                </div>
                <div>
                    <strong>Desa:</strong><br>
                    <code class="bg-gray-100 p-1 rounded">GET /api/wilayah/villages/{districtCode}</code>
                </div>
            </div>
        </div>

        <!-- Dropdown Test -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">Test Dropdown Cascade</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Provinsi</label>
                    <select id="test_provinsi" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Provinsi</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kabupaten/Kota</label>
                    <select id="test_kabupaten" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Kabupaten/Kota</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kecamatan</label>
                    <select id="test_kecamatan" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Kecamatan</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Desa/Kelurahan</label>
                    <select id="test_desa" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Desa/Kelurahan</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-bold mb-4">API Response</h2>
                <pre id="result" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
Klik tombol di atas untuk test API...
                </pre>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-bold mb-4">Console Log</h2>
                <div id="console" class="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-auto max-h-96 font-mono">
                    <div>Console output akan muncul di sini...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Setup CSRF token for Laravel
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // API Configuration - Using Laravel WilayahController
        const API_BASE = '/api/wilayah';
        
        // Console logging
        function logToConsole(message, type = 'info') {
            const consoleDiv = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400';
            
            consoleDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        // Display result function
        function displayResult(data, title) {
            const resultElement = document.getElementById('result');
            resultElement.textContent = `=== ${title} ===\n\n` + JSON.stringify(data, null, 2);
        }

        // Fetch data with error handling
        async function fetchData(url, title) {
            try {
                logToConsole(`Fetching: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResult(data, title);
                logToConsole(`Success: ${title} - ${data.data ? data.data.length : 0} items`, 'success');
                
                // Return data for further processing
                if (data.data) {
                    return data.data;
                }
                return [];
            } catch (error) {
                console.error('Error:', error);
                logToConsole(`Error: ${error.message}`, 'error');
                displayResult({
                    error: error.message,
                    url: url
                }, `ERROR - ${title}`);
                return [];
            }
        }

        // Test functions
        async function testProvinces() {
            const data = await fetchData(`${API_BASE}/provinces`, 'Test Provinsi');
            
            // Populate dropdown
            const select = document.getElementById('test_provinsi');
            select.innerHTML = '<option value="">Pilih Provinsi</option>';
            
            data.forEach(province => {
                const option = document.createElement('option');
                option.value = province.kode_wilayah || province.code;
                option.textContent = province.nama_wilayah || province.name;
                select.appendChild(option);
            });
        }

        async function testRegencies() {
            // Test dengan Jawa Tengah (kode: 33)
            await fetchData(`${API_BASE}/regencies/33`, 'Test Kabupaten - Jawa Tengah');
        }

        async function testDistricts() {
            // Test dengan Purworejo (kode: 33.06)
            await fetchData(`${API_BASE}/districts/33.06`, 'Test Kecamatan - Purworejo');
        }

        async function testVillages() {
            // Test dengan Bagelen (kode: 33.06.01)
            await fetchData(`${API_BASE}/villages/33.06.01`, 'Test Desa - Bagelen');
        }

        // Dropdown cascade functions
        async function loadRegencies(provinceCode) {
            const data = await fetchData(`${API_BASE}/regencies/${provinceCode}`, `Kabupaten - ${provinceCode}`);
            
            const select = document.getElementById('test_kabupaten');
            select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
            
            // Clear dependent dropdowns
            document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
            document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(regency => {
                const option = document.createElement('option');
                option.value = regency.kode_wilayah || regency.code;
                option.textContent = regency.nama_wilayah || regency.name;
                select.appendChild(option);
            });
        }

        async function loadDistricts(regencyCode) {
            const data = await fetchData(`${API_BASE}/districts/${regencyCode}`, `Kecamatan - ${regencyCode}`);
            
            const select = document.getElementById('test_kecamatan');
            select.innerHTML = '<option value="">Pilih Kecamatan</option>';
            
            // Clear dependent dropdown
            document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(district => {
                const option = document.createElement('option');
                option.value = district.kode_wilayah || district.code;
                option.textContent = district.nama_wilayah || district.name;
                select.appendChild(option);
            });
        }

        async function loadVillages(districtCode) {
            const data = await fetchData(`${API_BASE}/villages/${districtCode}`, `Desa - ${districtCode}`);
            
            const select = document.getElementById('test_desa');
            select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(village => {
                const option = document.createElement('option');
                option.value = village.kode_wilayah || village.code;
                option.textContent = village.nama_wilayah || village.name;
                select.appendChild(option);
            });
        }

        // Event listeners for dropdown cascade
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('Page loaded, initializing...', 'info');
            
            // Load provinces on page load
            testProvinces();

            // Province change
            document.getElementById('test_provinsi').addEventListener('change', function() {
                if (this.value) {
                    loadRegencies(this.value);
                } else {
                    document.getElementById('test_kabupaten').innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
                    document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // Regency change
            document.getElementById('test_kabupaten').addEventListener('change', function() {
                if (this.value) {
                    loadDistricts(this.value);
                } else {
                    document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // District change
            document.getElementById('test_kecamatan').addEventListener('change', function() {
                if (this.value) {
                    loadVillages(this.value);
                } else {
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });
        });
    </script>
</body>
</html>
