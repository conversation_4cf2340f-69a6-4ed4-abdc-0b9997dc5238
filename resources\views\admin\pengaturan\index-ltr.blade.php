@extends('layouts.admin-ltr')

@section('title', 'Pengaturan')

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Pengaturan</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" onclick="saveSettings()">
                <i class="bi bi-check-circle"></i> Simpan Pengaturan
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                <i class="bi bi-arrow-clockwise"></i> Reset
            </button>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<div class="row">
    <div class="col-12 col-lg-8">
        <!-- General Settings -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Pengaturan Umum</h5>
            </div>
            <div class="card-body">
                <form id="generalSettings">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Nama Aplikasi</label>
                            <input type="text" name="app_name" value="PLUT Purworejo" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Versi Aplikasi</label>
                            <input type="text" name="app_version" value="1.0.0" class="form-control" readonly>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Deskripsi Aplikasi</label>
                            <textarea name="app_description" rows="3" class="form-control">Platform Layanan Usaha Terpadu (PLUT) Kabupaten Purworejo untuk mendukung pengembangan UMKM lokal</textarea>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Email Kontak</label>
                            <input type="email" name="contact_email" value="<EMAIL>" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Nomor Telepon</label>
                            <input type="tel" name="contact_phone" value="+62 275 321123" class="form-control">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Alamat</label>
                            <textarea name="address" rows="2" class="form-control">Jl. Jenderal Sudirman No. 1, Purworejo, Jawa Tengah 54111</textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- UMKM Settings -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Pengaturan UMKM</h5>
            </div>
            <div class="card-body">
                <form id="umkmSettings">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Maksimal UMKM per Bulan</label>
                            <input type="number" name="max_umkm_per_month" value="100" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Waktu Verifikasi (Hari)</label>
                            <input type="number" name="verification_days" value="7" class="form-control">
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoVerification" checked>
                                <label class="form-check-label" for="autoVerification">
                                    Verifikasi Otomatis untuk UMKM dengan Dokumen Lengkap
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailNotification" checked>
                                <label class="form-check-label" for="emailNotification">
                                    Kirim Email Notifikasi ke UMKM
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="publicRegistration" checked>
                                <label class="form-check-label" for="publicRegistration">
                                    Buka Pendaftaran UMKM untuk Publik
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- System Settings -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Pengaturan Sistem</h5>
            </div>
            <div class="card-body">
                <form id="systemSettings">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Timezone</label>
                            <select name="timezone" class="form-select">
                                <option value="Asia/Jakarta" selected>Asia/Jakarta (WIB)</option>
                                <option value="Asia/Makassar">Asia/Makassar (WITA)</option>
                                <option value="Asia/Jayapura">Asia/Jayapura (WIT)</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Bahasa Default</label>
                            <select name="default_language" class="form-select">
                                <option value="id" selected>Bahasa Indonesia</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Maksimal Upload File (MB)</label>
                            <input type="number" name="max_file_size" value="10" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Session Timeout (Menit)</label>
                            <input type="number" name="session_timeout" value="120" class="form-control">
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                <label class="form-check-label" for="maintenanceMode">
                                    Mode Maintenance
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="debugMode">
                                <label class="form-check-label" for="debugMode">
                                    Mode Debug (Development Only)
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Pengaturan Email</h5>
            </div>
            <div class="card-body">
                <form id="emailSettings">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">SMTP Host</label>
                            <input type="text" name="smtp_host" value="smtp.gmail.com" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">SMTP Port</label>
                            <input type="number" name="smtp_port" value="587" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">SMTP Username</label>
                            <input type="email" name="smtp_username" value="<EMAIL>" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">SMTP Password</label>
                            <input type="password" name="smtp_password" value="••••••••" class="form-control">
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="smtpEncryption" checked>
                                <label class="form-check-label" for="smtpEncryption">
                                    Gunakan Enkripsi TLS
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="button" class="btn btn-outline-primary" onclick="testEmail()">
                                <i class="bi bi-envelope"></i> Test Koneksi Email
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-12 col-lg-4">
        <!-- System Info -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Informasi Sistem</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-primary text-primary">
                                <i class="bi bi-server"></i>
                            </div>
                            <h6 class="mb-1">Server</h6>
                            <p class="mb-0 text-muted small">Apache 2.4</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-success text-success">
                                <i class="bi bi-code-slash"></i>
                            </div>
                            <h6 class="mb-1">PHP</h6>
                            <p class="mb-0 text-muted small">8.2.0</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-warning text-warning">
                                <i class="bi bi-database"></i>
                            </div>
                            <h6 class="mb-1">Database</h6>
                            <p class="mb-0 text-muted small">MySQL 8.0</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="widget-icon mx-auto mb-2 bg-light-info text-info">
                                <i class="bi bi-hdd"></i>
                            </div>
                            <h6 class="mb-1">Storage</h6>
                            <p class="mb-0 text-muted small">15.2GB / 50GB</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup & Restore -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Backup & Restore</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="createBackup()">
                        <i class="bi bi-download"></i> Buat Backup
                    </button>
                    <button class="btn btn-outline-primary" onclick="downloadBackup()">
                        <i class="bi bi-cloud-download"></i> Download Backup
                    </button>
                    <button class="btn btn-warning" onclick="restoreBackup()">
                        <i class="bi bi-upload"></i> Restore Backup
                    </button>
                </div>
                <hr>
                <div class="small text-muted">
                    <p class="mb-1"><strong>Backup Terakhir:</strong></p>
                    <p class="mb-0">25 Juli 2024, 14:30 WIB</p>
                </div>
            </div>
        </div>

        <!-- Cache Management -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Manajemen Cache</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="clearCache()">
                        <i class="bi bi-arrow-clockwise"></i> Clear All Cache
                    </button>
                    <button class="btn btn-outline-success" onclick="clearViewCache()">
                        <i class="bi bi-eye"></i> Clear View Cache
                    </button>
                    <button class="btn btn-outline-success" onclick="clearRouteCache()">
                        <i class="bi bi-signpost"></i> Clear Route Cache
                    </button>
                    <button class="btn btn-outline-success" onclick="clearConfigCache()">
                        <i class="bi bi-gear"></i> Clear Config Cache
                    </button>
                </div>
            </div>
        </div>

        <!-- Security -->
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Keamanan</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                        <label class="form-check-label" for="twoFactorAuth">
                            Two-Factor Authentication
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="loginAttempts" checked>
                        <label class="form-check-label" for="loginAttempts">
                            Batasi Percobaan Login
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="ipWhitelist">
                        <label class="form-check-label" for="ipWhitelist">
                            IP Whitelist untuk Admin
                        </label>
                    </div>
                </div>
                <div class="d-grid">
                    <button class="btn btn-outline-danger" onclick="viewSecurityLogs()">
                        <i class="bi bi-shield-exclamation"></i> Lihat Log Keamanan
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function saveSettings() {
    // Collect all form data
    const generalData = new FormData(document.getElementById('generalSettings'));
    const umkmData = new FormData(document.getElementById('umkmSettings'));
    const systemData = new FormData(document.getElementById('systemSettings'));
    const emailData = new FormData(document.getElementById('emailSettings'));
    
    console.log('Saving settings...');
    // Here you would make AJAX calls to save the settings
    
    // Show success message
    alert('Pengaturan berhasil disimpan!');
}

function resetSettings() {
    if (confirm('Reset semua pengaturan ke default? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Resetting settings...');
        location.reload();
    }
}

function testEmail() {
    console.log('Testing email connection...');
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';
    btn.disabled = true;
    
    // Simulate test
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Koneksi email berhasil!');
    }, 2000);
}

function createBackup() {
    if (confirm('Buat backup database dan file? Proses ini mungkin memakan waktu beberapa menit.')) {
        console.log('Creating backup...');
        alert('Backup sedang dibuat. Anda akan mendapat notifikasi setelah selesai.');
    }
}

function downloadBackup() {
    console.log('Downloading backup...');
    alert('Download backup dimulai...');
}

function restoreBackup() {
    if (confirm('Restore backup? Semua data saat ini akan diganti dengan data backup.')) {
        console.log('Restoring backup...');
        alert('Proses restore dimulai...');
    }
}

function clearCache() {
    console.log('Clearing all cache...');
    alert('Cache berhasil dibersihkan!');
}

function clearViewCache() {
    console.log('Clearing view cache...');
    alert('View cache berhasil dibersihkan!');
}

function clearRouteCache() {
    console.log('Clearing route cache...');
    alert('Route cache berhasil dibersihkan!');
}

function clearConfigCache() {
    console.log('Clearing config cache...');
    alert('Config cache berhasil dibersihkan!');
}

function viewSecurityLogs() {
    console.log('Opening security logs...');
    // Open security logs in modal or new page
}
</script>
@endpush
