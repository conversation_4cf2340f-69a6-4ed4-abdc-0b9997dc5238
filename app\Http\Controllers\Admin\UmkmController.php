<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Profil;
use App\Models\Usaha;
use App\Models\Legalitas;
use App\Exports\UmkmExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class UmkmController extends Controller
{
    public function index(Request $request)
    {
        $query = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm');

        // Apply URL filter if exists
        $initialFilter = null;
        if ($request->has('status')) {
            $query->where('verification_status', $request->status);
            $initialFilter = $request->status;
        }

        $umkms = $query->latest()->get();

        // Statistics (always from all data)
        $allUmkms = User::where('role', 'umkm')->get();
        $totalUmkm = $allUmkms->count();
        $umkmVerified = User::where('role', 'umkm')
            ->where('verification_status', 'verified')
            ->count();
        $umkmPending = User::where('role', 'umkm')
            ->where('verification_status', 'pending')
            ->count();
        $umkmRejected = User::where('role', 'umkm')
            ->where('verification_status', 'rejected')
            ->count();
        $umkmThisMonth = User::where('role', 'umkm')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        // Get unique kecamatan for filter (from all data)
        $kecamatanList = collect();
        foreach ($allUmkms as $umkm) {
            $kecamatan = $umkm->usaha->kecamatan_usaha ?? $umkm->profil->kecamatan ?? null;
            if ($kecamatan && !$kecamatanList->contains($kecamatan)) {
                $kecamatanList->push($kecamatan);
            }
        }
        $kecamatanList = $kecamatanList->sort()->values();

        return view('admin.umkm.index-clean', compact(
            'umkms',
            'totalUmkm',
            'umkmVerified',
            'umkmPending',
            'umkmRejected',
            'umkmThisMonth',
            'kecamatanList',
            'initialFilter'
        ));
    }

    public function indexLtr()
    {
        $umkms = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->latest()
            ->get();

        // Statistics
        $totalUmkm = $umkms->count();
        $umkmVerified = User::where('role', 'umkm')
            ->where('verification_status', 'verified')
            ->count();
        $umkmPending = User::where('role', 'umkm')
            ->where('verification_status', 'pending')
            ->count();
        $umkmRejected = User::where('role', 'umkm')
            ->where('verification_status', 'rejected')
            ->count();
        $umkmThisMonth = User::where('role', 'umkm')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return view('admin.umkm.index-clean', compact(
            'umkms',
            'totalUmkm',
            'umkmVerified',
            'umkmPending',
            'umkmRejected',
            'umkmThisMonth'
        ));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'nullable|string|min:8',
            'no_hp' => 'required|string|max:20',
            'nik' => 'required|string|size:16|unique:profils,nik',
            'jenis_kelamin' => 'required|in:Laki-laki,Perempuan',
            'foto_profil' => 'nullable|image|mimes:jpeg,jpg,png,gif|max:2048',
            'provinsi' => 'required|string|max:100',
            'kabupaten' => 'required|string|max:100',
            'kecamatan' => 'required|string|max:100',
            'desa' => 'required|string|max:100',
            'alamat_lengkap' => 'required|string|max:500',
            'nama_usaha' => 'required|string|max:255',
            'bidang_usaha' => 'required|in:makanan_minuman,kerajinan_tangan,perdagangan,jasa',
            'provinsi_usaha' => 'required|string|max:100',
            'kabupaten_usaha' => 'required|string|max:100',
            'kecamatan_usaha' => 'required|string|max:100',
            'desa_usaha' => 'required|string|max:100',
            'alamat_lengkap_usaha' => 'required|string|max:500',
            'other_legalitas_nama.*' => 'nullable|string|max:255',
            'other_legalitas_nomor.*' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Create User
            $userData = [
                'nama' => $request->nama,
                'email' => $request->email,
                'no_hp' => $request->no_hp,
                'role' => 'umkm',
            ];

            if ($request->password) {
                $userData['password'] = Hash::make($request->password);
            } else {
                $userData['password'] = Hash::make('password123'); // Default password
            }

            $user = User::create($userData);

            // Handle photo upload
            $fotoPath = null;
            if ($request->hasFile('foto_profil')) {
                $foto = $request->file('foto_profil');
                $filename = 'profile_' . $user->id . '_' . time() . '.' . $foto->getClientOriginalExtension();
                $fotoPath = $foto->storeAs('profile_photos', $filename, 'public');
            }

            // Get province and kabupaten names from codes
            $provinsiName = $this->getWilayahName('provinces', $request->provinsi);
            $kabupatenName = $this->getWilayahName('regencies', $request->kabupaten, $request->provinsi);
            $kecamatanName = $this->getWilayahName('districts', $request->kecamatan, $request->kabupaten);
            $desaName = $this->getWilayahName('villages', $request->desa, $request->kecamatan);

            // Create Profil
            $profilData = [
                'user_id' => $user->id,
                'nik' => $request->nik,
                'jenis_kelamin' => $request->jenis_kelamin,
                'provinsi' => $provinsiName ?: $request->provinsi,
                'kabupaten' => $kabupatenName ?: $request->kabupaten,
                'kecamatan' => $kecamatanName ?: $request->kecamatan,
                'desa' => $desaName ?: $request->desa,
                'alamat_lengkap' => $request->alamat_lengkap,
            ];

            if ($fotoPath) {
                $profilData['foto_profil'] = $fotoPath;
            }

            Profil::create($profilData);

            // Process media sosial
            $mediaSosialData = [];
            if ($request->has('media_sosial_types')) {
                foreach ($request->media_sosial_types as $type) {
                    $fieldName = strtolower($type) . '_username';
                    if ($type === 'WhatsApp') {
                        $fieldName = 'whatsapp_number';
                    }

                    if ($request->has($fieldName) && !empty($request->$fieldName)) {
                        $mediaSosialData[] = $type . ': ' . $request->$fieldName;
                    }
                }
            }

            // Get business address names from codes
            $provinsiUsahaName = $this->getWilayahName('provinces', $request->provinsi_usaha);
            $kabupatenUsahaName = $this->getWilayahName('regencies', $request->kabupaten_usaha, $request->provinsi_usaha);
            $kecamatanUsahaName = $this->getWilayahName('districts', $request->kecamatan_usaha, $request->kabupaten_usaha);
            $desaUsahaName = $this->getWilayahName('villages', $request->desa_usaha, $request->kecamatan_usaha);

            // Create Usaha
            Usaha::create([
                'user_id' => $user->id,
                'nama_usaha' => $request->nama_usaha,
                'nama_merk' => $request->nama_merk,
                'bidang_usaha' => $request->bidang_usaha,
                'deskripsi' => $request->deskripsi,
                'media_sosial' => implode('; ', $mediaSosialData),
                'provinsi_usaha' => $provinsiUsahaName ?: $request->provinsi_usaha,
                'kabupaten_usaha' => $kabupatenUsahaName ?: $request->kabupaten_usaha,
                'kecamatan_usaha' => $kecamatanUsahaName ?: $request->kecamatan_usaha,
                'desa_usaha' => $desaUsahaName ?: $request->desa_usaha,
                'alamat_lengkap_usaha' => $request->alamat_lengkap_usaha,
            ]);

            // Create Legalitas (if provided)
            if ($request->has('legalitas_types')) {
                foreach ($request->legalitas_types as $type) {
                    $fieldName = strtolower($type) . '_nomor';

                    if ($request->has($fieldName) && !empty($request->$fieldName)) {
                        Legalitas::create([
                            'user_id' => $user->id,
                            'nama_legalitas' => $type,
                            'nomor_legalitas' => $request->$fieldName,
                        ]);
                    }
                }
            }

            // Create Other Legalitas (if provided)
            if ($request->has('other_legalitas_nama') && $request->has('other_legalitas_nomor')) {
                $namaArray = $request->other_legalitas_nama;
                $nomorArray = $request->other_legalitas_nomor;

                for ($i = 0; $i < count($namaArray); $i++) {
                    if (!empty($namaArray[$i]) && !empty($nomorArray[$i])) {
                        Legalitas::create([
                            'user_id' => $user->id,
                            'nama_legalitas' => $namaArray[$i],
                            'nomor_legalitas' => $nomorArray[$i],
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'UMKM berhasil ditambahkan!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $umkm = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->findOrFail($id);

        return view('admin.umkm.show-ltr', compact('umkm'));
    }

    public function detail($id)
    {
        try {
            $umkm = User::with(['profil', 'usaha', 'legalitas'])
                ->where('role', 'umkm')
                ->findOrFail($id);

            $html = view('admin.umkm.partials.detail', compact('umkm'))->render();

            return response()->json([
                'success' => true,
                'html' => $html,
                'umkm' => $umkm
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'UMKM tidak ditemukan'
            ], 404);
        }
    }

    public function edit($id)
    {
        try {
            $umkm = User::with(['profil', 'usaha', 'legalitas'])
                ->where('role', 'umkm')
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $umkm->id,
                    'nama' => $umkm->nama,
                    'email' => $umkm->email,
                    'no_hp' => $umkm->no_hp,
                    'nik' => $umkm->profil->nik ?? '',
                    'jenis_kelamin' => $umkm->profil->jenis_kelamin ?? '',
                    'provinsi' => $umkm->profil->provinsi ?? '',
                    'kabupaten' => $umkm->profil->kabupaten ?? '',
                    'kecamatan' => $umkm->profil->kecamatan ?? '',
                    'desa' => $umkm->profil->desa ?? '',
                    'alamat_lengkap' => $umkm->profil->alamat_lengkap ?? '',
                    'nama_usaha' => $umkm->usaha->nama_usaha ?? '',
                    'nama_merk' => $umkm->usaha->nama_merk ?? '',
                    'bidang_usaha' => $umkm->usaha->bidang_usaha ?? '',
                    'deskripsi' => $umkm->usaha->deskripsi ?? '',
                    'provinsi_usaha' => $umkm->usaha->provinsi_usaha ?? '',
                    'kabupaten_usaha' => $umkm->usaha->kabupaten_usaha ?? '',
                    'kecamatan_usaha' => $umkm->usaha->kecamatan_usaha ?? '',
                    'desa_usaha' => $umkm->usaha->desa_usaha ?? '',
                    'alamat_lengkap_usaha' => $umkm->usaha->alamat_lengkap_usaha ?? '',
                    'legalitas' => $umkm->legalitas->map(function($legal) {
                        return [
                            'nama_legalitas' => $legal->nama_legalitas,
                            'nomor_legalitas' => $legal->nomor_legalitas
                        ];
                    })
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'UMKM tidak ditemukan'
            ], 404);
        }
    }

    public function editForm($id)
    {
        try {
            $umkm = User::with(['profil', 'usaha', 'legalitas'])
                ->where('role', 'umkm')
                ->findOrFail($id);

            $html = view('admin.umkm.partials.edit-form', compact('umkm'))->render();

            return response()->json([
                'success' => true,
                'html' => $html
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'UMKM tidak ditemukan'
            ], 404);
        }
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'no_hp' => 'required|string|max:20',
            'nik' => 'required|string|size:16|unique:profils,nik,' . $id . ',user_id',
            'jenis_kelamin' => 'required|in:Laki-laki,Perempuan',
            'provinsi' => 'required|string|max:100',
            'kabupaten' => 'required|string|max:100',
            'kecamatan' => 'required|string|max:100',
            'desa' => 'required|string|max:100',
            'alamat_lengkap' => 'required|string|max:500',
            'nama_usaha' => 'required|string|max:255',
            'bidang_usaha' => 'required|in:makanan_minuman,kerajinan_tangan,perdagangan,jasa',
            'provinsi_usaha' => 'required|string|max:100',
            'kabupaten_usaha' => 'required|string|max:100',
            'kecamatan_usaha' => 'required|string|max:100',
            'desa_usaha' => 'required|string|max:100',
            'alamat_lengkap_usaha' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = User::findOrFail($id);

            // Update User
            $user->update([
                'nama' => $request->nama,
                'email' => $request->email,
                'no_hp' => $request->no_hp,
            ]);

            // Update password if provided
            if ($request->password) {
                $user->update(['password' => Hash::make($request->password)]);
            }

            // Get province and kabupaten names from codes
            $provinsiName = $this->getWilayahName('provinces', $request->provinsi);
            $kabupatenName = $this->getWilayahName('regencies', $request->kabupaten, $request->provinsi);
            $kecamatanName = $this->getWilayahName('districts', $request->kecamatan, $request->kabupaten);
            $desaName = $this->getWilayahName('villages', $request->desa, $request->kecamatan);

            // Update Profil
            $user->profil()->updateOrCreate(
                ['user_id' => $user->id],
                [
                    'nik' => $request->nik,
                    'jenis_kelamin' => $request->jenis_kelamin,
                    'provinsi' => $provinsiName ?: $request->provinsi,
                    'kabupaten' => $kabupatenName ?: $request->kabupaten,
                    'kecamatan' => $kecamatanName ?: $request->kecamatan,
                    'desa' => $desaName ?: $request->desa,
                    'alamat_lengkap' => $request->alamat_lengkap,
                ]
            );

            // Get business address names from codes
            $provinsiUsahaName = $this->getWilayahName('provinces', $request->provinsi_usaha);
            $kabupatenUsahaName = $this->getWilayahName('regencies', $request->kabupaten_usaha, $request->provinsi_usaha);
            $kecamatanUsahaName = $this->getWilayahName('districts', $request->kecamatan_usaha, $request->kabupaten_usaha);
            $desaUsahaName = $this->getWilayahName('villages', $request->desa_usaha, $request->kecamatan_usaha);

            // Process media sosial for update
            $mediaSosialData = [];
            if ($request->has('media_sosial_types')) {
                foreach ($request->media_sosial_types as $type) {
                    $fieldName = strtolower($type) . '_username';
                    if ($type === 'WhatsApp') {
                        $fieldName = 'whatsapp_number';
                    }

                    if ($request->has($fieldName) && !empty($request->$fieldName)) {
                        $mediaSosialData[] = $type . ': ' . $request->$fieldName;
                    }
                }
            }

            // Update Usaha
            $user->usaha()->updateOrCreate(
                ['user_id' => $user->id],
                [
                    'nama_usaha' => $request->nama_usaha,
                    'nama_merk' => $request->nama_merk,
                    'bidang_usaha' => $request->bidang_usaha,
                    'deskripsi' => $request->deskripsi,
                    'media_sosial' => implode('; ', $mediaSosialData),
                    'provinsi_usaha' => $provinsiUsahaName ?: $request->provinsi_usaha,
                    'kabupaten_usaha' => $kabupatenUsahaName ?: $request->kabupaten_usaha,
                    'kecamatan_usaha' => $kecamatanUsahaName ?: $request->kecamatan_usaha,
                    'desa_usaha' => $desaUsahaName ?: $request->desa_usaha,
                    'alamat_lengkap_usaha' => $request->alamat_lengkap_usaha,
                ]
            );

            // Update Legalitas
            // First, delete existing legalitas
            $user->legalitas()->delete();

            // Then create new ones if provided
            if ($request->has('legalitas_types')) {
                foreach ($request->legalitas_types as $type) {
                    $fieldName = strtolower($type) . '_nomor';

                    if ($request->has($fieldName) && !empty($request->$fieldName)) {
                        Legalitas::create([
                            'user_id' => $user->id,
                            'nama_legalitas' => $type,
                            'nomor_legalitas' => $request->$fieldName,
                        ]);
                    }
                }
            }

            // Create Other Legalitas (if provided)
            if ($request->has('other_legalitas_nama') && $request->has('other_legalitas_nomor')) {
                $namaArray = $request->other_legalitas_nama;
                $nomorArray = $request->other_legalitas_nomor;

                for ($i = 0; $i < count($namaArray); $i++) {
                    if (!empty($namaArray[$i]) && !empty($nomorArray[$i])) {
                        Legalitas::create([
                            'user_id' => $user->id,
                            'nama_legalitas' => $namaArray[$i],
                            'nomor_legalitas' => $nomorArray[$i],
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'UMKM berhasil diupdate!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $user = User::with(['profil', 'usaha', 'legalitas'])
                ->where('role', 'umkm')
                ->findOrFail($id);

            // Delete related data
            $user->profil()->delete();
            $user->usaha()->delete();
            $user->legalitas()->delete();

            // Delete user
            $user->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'UMKM berhasil dihapus!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function approveMultiple(Request $request)
    {
        try {
            $ids = $request->ids;

            if (empty($ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak ada UMKM yang dipilih'
                ], 400);
            }

            // Update verification status to verified
            $updated = User::whereIn('id', $ids)
                ->where('role', 'umkm')
                ->update([
                    'verification_status' => 'verified',
                    'verified_at' => now(),
                    'verified_by' => Auth::id(),
                    'verification_notes' => 'Disetujui melalui bulk approval'
                ]);

            return response()->json([
                'success' => true,
                'message' => $updated . ' UMKM berhasil disetujui!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function rejectMultiple(Request $request)
    {
        try {
            $ids = $request->ids;
            $reason = $request->reason;

            if (empty($ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak ada UMKM yang dipilih'
                ], 400);
            }

            if (empty($reason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Alasan penolakan harus diisi'
                ], 400);
            }

            // Update verification status to rejected
            $updated = User::whereIn('id', $ids)
                ->where('role', 'umkm')
                ->update([
                    'verification_status' => 'rejected',
                    'verified_at' => now(),
                    'verified_by' => Auth::id(),
                    'verification_notes' => $reason
                ]);

            return response()->json([
                'success' => true,
                'message' => $updated . ' UMKM berhasil ditolak!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function approve($id)
    {
        try {
            $user = User::where('role', 'umkm')->findOrFail($id);

            $user->update([
                'verification_status' => 'verified',
                'verified_at' => now(),
                'verified_by' => Auth::id(),
                'verification_notes' => 'Disetujui oleh admin'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'UMKM berhasil disetujui!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function reject(Request $request, $id)
    {
        try {
            $user = User::where('role', 'umkm')->findOrFail($id);

            $user->update([
                'verification_status' => 'rejected',
                'verified_at' => now(),
                'verified_by' => Auth::id(),
                'verification_notes' => $request->reason ?? 'Ditolak oleh admin'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'UMKM berhasil ditolak!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function filter(Request $request)
    {
        try {
            $query = User::with(['profil', 'usaha', 'legalitas'])
                ->where('role', 'umkm');

            // Filter by status
            if ($request->filled('status')) {
                $query->where('verification_status', $request->status);
            }

            // Filter by bidang usaha
            if ($request->filled('bidang')) {
                $query->whereHas('usaha', function ($q) use ($request) {
                    $q->where('bidang_usaha', $request->bidang);
                });
            }

            // Filter by kecamatan
            if ($request->filled('kecamatan')) {
                $query->where(function ($q) use ($request) {
                    $q->whereHas('usaha', function ($subQ) use ($request) {
                        $subQ->where('kecamatan_usaha', $request->kecamatan);
                    })->orWhereHas('profil', function ($subQ) use ($request) {
                        $subQ->where('kecamatan', $request->kecamatan);
                    });
                });
            }

            // Search by name, email, or business name
            if ($request->filled('search')) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('nama', 'like', "%{$searchTerm}%")
                      ->orWhere('email', 'like', "%{$searchTerm}%")
                      ->orWhereHas('usaha', function ($subQ) use ($searchTerm) {
                          $subQ->where('nama_usaha', 'like', "%{$searchTerm}%");
                      });
                });
            }

            $umkms = $query->latest()->get();

            // Generate HTML for table rows
            $html = '';
            foreach ($umkms as $umkm) {
                $html .= view('admin.umkm.partials.table-row', compact('umkm'))->render();
            }

            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $umkms->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function exportExcel()
    {
        $umkms = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->get();

        $filename = 'Data_UMKM_PLUT_Purworejo_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(new UmkmExport($umkms), $filename);
    }

    public function exportUser($id)
    {
        $umkm = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->findOrFail($id);

        $filename = 'Data_UMKM_' . str_replace(' ', '_', $umkm->nama) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(new UmkmExport(collect([$umkm])), $filename);
    }

    public function exportPDF()
    {
        $umkms = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->get();

        $html = view('admin.umkm.export.pdf', compact('umkms'))->render();

        // For now, return HTML that can be printed as PDF
        return response($html)
            ->header('Content-Type', 'text/html')
            ->header('Content-Disposition', 'inline; filename="Data_UMKM_PLUT_Purworejo_' . date('Y-m-d_H-i-s') . '.html"');
    }

    /**
     * Export UMKM data to Excel/CSV (Legacy method).
     */
    public function export()
    {
        $umkms = User::with(['profil', 'usaha', 'legalitas'])
            ->where('role', 'umkm')
            ->get();

        $filename = 'Data_UMKM_PLUT_Purworejo_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($umkms) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // Header row
            fputcsv($file, [
                'Nama',
                'Email',
                'NIK',
                'Nomor HP',
                'Alamat Pribadi',
                'Nama Usaha',
                'Merk Usaha',
                'Bidang Usaha',
                'Alamat Usaha',
                'Ijin yang Dimiliki',
            ]);

            // Data rows
            foreach ($umkms as $umkm) {
                // Format alamat pribadi
                $alamatPribadi = '';
                if ($umkm->profil) {
                    $alamatPribadi = $umkm->profil->alamat_lengkap . ', ' .
                                   $umkm->profil->desa . ', ' .
                                   $umkm->profil->kecamatan . ', ' .
                                   $umkm->profil->kabupaten . ', ' .
                                   $umkm->profil->provinsi;
                }

                // Format alamat usaha
                $alamatUsaha = '';
                if ($umkm->usaha) {
                    $alamatUsaha = $umkm->usaha->alamat_usaha . ', ' .
                                 $umkm->usaha->kelurahan . ', ' .
                                 $umkm->usaha->kecamatan . ', ' .
                                 ($umkm->usaha->kode_pos ? 'Kode Pos: ' . $umkm->usaha->kode_pos : '');
                }

                // Format legalitas
                $legalitas = '';
                if ($umkm->legalitas->count() > 0) {
                    $legalitas = $umkm->legalitas->map(function ($legal) {
                        return $legal->nama_legalitas . '(' . $legal->nomor_legalitas . ')';
                    })->implode(', ');
                }

                // Format bidang usaha
                $bidangUsaha = '';
                if ($umkm->usaha && $umkm->usaha->bidang_usaha) {
                    $bidangUsaha = ucwords(str_replace('_', ' ', $umkm->usaha->bidang_usaha));
                }

                fputcsv($file, [
                    $umkm->nama ?? '',
                    $umkm->email ?? '',
                    $umkm->profil->nik ?? '',
                    $umkm->no_hp ?? '',
                    $alamatPribadi,
                    $umkm->usaha->nama_usaha ?? '',
                    $umkm->usaha->nama_merk ?? '',
                    $bidangUsaha,
                    $alamatUsaha,
                    $legalitas,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Helper method to get wilayah name from code
     */
    private function getWilayahName($type, $code, $parentCode = null)
    {
        try {
            $url = '';
            switch ($type) {
                case 'provinces':
                    $url = '/api/wilayah/provinces';
                    break;
                case 'regencies':
                    $url = "/api/wilayah/regencies/{$parentCode}";
                    break;
                case 'districts':
                    $url = "/api/wilayah/districts/{$parentCode}";
                    break;
                case 'villages':
                    $url = "/api/wilayah/villages/{$parentCode}";
                    break;
            }

            $response = Http::get(url($url));
            if ($response->successful()) {
                $data = $response->json();
                $items = $data['data'] ?? $data;

                foreach ($items as $item) {
                    $itemCode = $item['kode_wilayah'] ?? $item['code'];
                    if ($itemCode === $code) {
                        return $item['nama_wilayah'] ?? $item['name'];
                    }
                }
            }
        } catch (\Exception $e) {
            // Return original code if API fails
        }

        return null;
    }
}
