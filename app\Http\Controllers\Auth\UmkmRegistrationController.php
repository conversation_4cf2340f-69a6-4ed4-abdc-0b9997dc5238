<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\UmkmRegistrationRequest;
use App\Models\User;
use App\Models\Profil;
use App\Models\Usaha;
use App\Models\Legalitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UmkmRegistrationController extends Controller
{
    /**
     * Show the registration form.
     */
    public function showRegistrationForm()
    {
        return view('auth.register-new');
    }

    /**
     * Handle UMKM registration.
     */
    public function register(UmkmRegistrationRequest $request)
    {
        try {
            // Debug log
            Log::info('Registration attempt started', [
                'email' => $request->email,
                'nama' => $request->nama,
                'all_data' => $request->all()
            ]);

            DB::beginTransaction();

            // Create User
            $user = User::create([
                'nama' => $request->nama,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'no_hp' => $request->no_hp,
                'role' => 'umkm',
            ]);

            // Create Profil
            Profil::create([
                'user_id' => $user->id,
                'nik' => $request->nik,
                'jenis_kelamin' => $request->jenis_kelamin,
                'provinsi' => $request->provinsi,
                'kabupaten' => $request->kabupaten,
                'kecamatan' => $request->kecamatan,
                'desa' => $request->desa,
                'alamat_lengkap' => $request->alamat_lengkap,
            ]);

            // Process Media Sosial
            $mediaSosialData = [];
            $mediaSosialTypes = $request->input('media_sosial_types', []);

            foreach ($mediaSosialTypes as $type) {
                $fieldName = strtolower($type);
                switch ($type) {
                    case 'Instagram':
                        $username = $request->input('instagram_username');
                        if (!empty($username)) {
                            $mediaSosialData[] = "Instagram: {$username}";
                        }
                        break;
                    case 'Facebook':
                        $page = $request->input('facebook_page');
                        if (!empty($page)) {
                            $mediaSosialData[] = "Facebook: {$page}";
                        }
                        break;
                    case 'WhatsApp':
                        $number = $request->input('whatsapp_number');
                        if (!empty($number)) {
                            $mediaSosialData[] = "WhatsApp: {$number}";
                        }
                        break;
                    case 'TikTok':
                        $username = $request->input('tiktok_username');
                        if (!empty($username)) {
                            $mediaSosialData[] = "TikTok: {$username}";
                        }
                        break;
                    case 'YouTube':
                        $channel = $request->input('youtube_channel');
                        if (!empty($channel)) {
                            $mediaSosialData[] = "YouTube: {$channel}";
                        }
                        break;
                    case 'Website':
                        $url = $request->input('website_url');
                        if (!empty($url)) {
                            $mediaSosialData[] = "Website: {$url}";
                        }
                        break;
                }
            }

            // Create Usaha
            Usaha::create([
                'user_id' => $user->id,
                'nama_usaha' => $request->nama_usaha,
                'nama_merk' => $request->nama_merk,
                'bidang_usaha' => $request->bidang_usaha,
                'deskripsi' => $request->deskripsi,
                'media_sosial' => implode('; ', $mediaSosialData),
                'provinsi_usaha' => 'Jawa Tengah',
                'kabupaten_usaha' => 'Purworejo',
                'kecamatan_usaha' => $request->kecamatan_usaha,
                'desa_usaha' => $request->desa_usaha,
                'alamat_lengkap_usaha' => $request->alamat_lengkap_usaha,
            ]);

            // Create Legalitas (if provided)
            $legalitasTypes = $request->input('legalitas_types', []);

            foreach ($legalitasTypes as $type) {
                $fieldName = strtolower($type) . '_nomor';
                $nomor = $request->input($fieldName);

                if (!empty($nomor)) {
                    if ($type === 'Lainnya') {
                        $namaLegalitas = $request->input('lainnya_nama');
                        if (!empty($namaLegalitas)) {
                            Legalitas::create([
                                'user_id' => $user->id,
                                'nama_legalitas' => $namaLegalitas,
                                'nomor_legalitas' => $nomor,
                            ]);
                        }
                    } else {
                        Legalitas::create([
                            'user_id' => $user->id,
                            'nama_legalitas' => $type,
                            'nomor_legalitas' => $nomor,
                        ]);
                    }
                }
            }

            DB::commit();

            Log::info('Registration completed successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'nama' => $user->nama
            ]);

            return redirect()->route('auth.login')
                ->with('success', 'Pendaftaran berhasil! Silakan login dengan akun Anda. Data usaha Anda akan diverifikasi dalam 1-3 hari kerja.');

        } catch (\Exception $e) {
            DB::rollback();

            // Enhanced debug logging
            Log::error('UMKM Registration Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $request->email ?? 'unknown',
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

            return back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan saat mendaftar: ' . $e->getMessage());
        }
    }

    /**
     * Check if NIK is already registered (AJAX).
     */
    public function checkNik(Request $request)
    {
        $nik = $request->input('nik');

        if (!$nik) {
            return response()->json(['available' => false, 'message' => 'NIK tidak boleh kosong']);
        }

        $exists = Profil::where('nik', $nik)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'NIK sudah terdaftar dalam sistem' : 'NIK tersedia'
        ]);
    }

    /**
     * Check if email is already registered (AJAX).
     */
    public function checkEmail(Request $request)
    {
        $email = $request->input('email');

        if (!$email) {
            return response()->json(['available' => false, 'message' => 'Email tidak boleh kosong']);
        }

        $exists = User::where('email', $email)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'Email sudah terdaftar' : 'Email tersedia'
        ]);
    }
}
