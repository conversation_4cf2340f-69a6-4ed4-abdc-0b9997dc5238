<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrasi - Platform UMKM</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-md">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="text-2xl font-bold text-blue-600">
                    Platform UMKM
                </div>
                <div class="hidden md:flex space-x-6">
                    <a href="/beranda" class="text-gray-600 hover:text-blue-600">Beranda</a>
                    <a href="/profil" class="text-gray-600 hover:text-blue-600">Profil</a>
                    <a href="/berita" class="text-gray-600 hover:text-blue-600">Berita</a>
                    <a href="/pelatihan" class="text-gray-600 hover:text-blue-600">Pelatihan</a>
                    <a href="/galeri" class="text-gray-600 hover:text-blue-600">Galeri</a>
                    <a href="/peta" class="text-gray-600 hover:text-blue-600">Peta UMKM</a>
                    <a href="/kontak" class="text-gray-600 hover:text-blue-600">Kontak</a>
                </div>
                <div class="flex space-x-2">
                    <a href="/login" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Masuk</a>
                    <a href="/registrasi" class="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50">Daftar</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Registration Form -->
    <section class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Daftar Akun UMKM
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Sudah punya akun?
                    <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">
                        Masuk di sini
                    </a>
                </p>
            </div>

            <form id="registrationForm" class="mt-8 space-y-8 bg-white p-8 rounded-lg shadow-md" action="#" method="POST">
                <!-- Data Pribadi -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-600 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white w-6 h-6 rounded-full inline-flex items-center justify-center text-sm mr-2">1</span>
                        Data Pribadi
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="nama_lengkap" class="block text-sm font-medium text-gray-700">Nama Lengkap *</label>
                            <input id="nama_lengkap" name="nama_lengkap" type="text" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Masukkan nama lengkap sesuai KTP">
                        </div>
                        <div>
                            <label for="nik" class="block text-sm font-medium text-gray-700">NIK (Nomor Induk Kependudukan) *</label>
                            <input id="nik" name="nik" type="text" maxlength="16" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="16 digit NIK">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                            <input id="email" name="email" type="email" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label for="nomor_hp" class="block text-sm font-medium text-gray-700">Nomor HP/WhatsApp *</label>
                            <input id="nomor_hp" name="nomor_hp" type="tel" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="08xxxxxxxxxx">
                        </div>
                    </div>

                    <!-- Alamat Pribadi - Bebas seluruh Indonesia -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-800 mb-3">Alamat Pribadi</h4>
                        <div class="p-4 bg-blue-50 border-l-4 border-blue-400 rounded mb-4">
                            <p class="text-sm text-blue-800">
                                <strong>Catatan:</strong> Alamat pribadi dapat berada di seluruh wilayah Indonesia.
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="provinsi_pribadi" class="block text-sm font-medium text-gray-700">Provinsi *</label>
                                <select id="provinsi_pribadi" name="provinsi_pribadi" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Provinsi</option>
                                </select>
                            </div>
                            <div>
                                <label for="kabupaten_pribadi" class="block text-sm font-medium text-gray-700">Kabupaten/Kota *</label>
                                <select id="kabupaten_pribadi" name="kabupaten_pribadi" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Kabupaten/Kota</option>
                                </select>
                            </div>
                            <div>
                                <label for="kecamatan_pribadi" class="block text-sm font-medium text-gray-700">Kecamatan *</label>
                                <select id="kecamatan_pribadi" name="kecamatan_pribadi" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Kecamatan</option>
                                </select>
                            </div>
                            <div>
                                <label for="desa_pribadi" class="block text-sm font-medium text-gray-700">Desa/Kelurahan *</label>
                                <select id="desa_pribadi" name="desa_pribadi" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Desa/Kelurahan</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="alamat_lengkap_pribadi" class="block text-sm font-medium text-gray-700">Alamat Lengkap *</label>
                            <textarea id="alamat_lengkap_pribadi" name="alamat_lengkap_pribadi" rows="3" required
                                      class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Jalan, RT/RW, No. Rumah, dan detail alamat lainnya"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Data Usaha -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-600 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white w-6 h-6 rounded-full inline-flex items-center justify-center text-sm mr-2">2</span>
                        Data Usaha
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="nama_usaha" class="block text-sm font-medium text-gray-700">Nama Usaha *</label>
                            <input id="nama_usaha" name="nama_usaha" type="text" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nama usaha/perusahaan">
                        </div>
                        <div>
                            <label for="nama_brand" class="block text-sm font-medium text-gray-700">Nama Merk/Brand</label>
                            <input id="nama_brand" name="nama_brand" type="text"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nama brand/merk produk (jika ada)">
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="bidang_usaha" class="block text-sm font-medium text-gray-700">Bidang Usaha *</label>
                        <select id="bidang_usaha" name="bidang_usaha" required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Pilih bidang usaha</option>
                            <option value="makanan_minuman">Makanan & Minuman</option>
                            <option value="fashion_tekstil">Fashion & Tekstil</option>
                            <option value="kerajinan_tangan">Kerajinan Tangan</option>
                            <option value="pertanian_perikanan">Pertanian & Perikanan</option>
                            <option value="jasa_perdagangan">Jasa & Perdagangan</option>
                            <option value="teknologi_digital">Teknologi & Digital</option>
                            <option value="kesehatan_kecantikan">Kesehatan & Kecantikan</option>
                            <option value="otomotif">Otomotif</option>
                            <option value="konstruksi">Konstruksi & Bangunan</option>
                            <option value="pendidikan">Pendidikan & Pelatihan</option>
                            <option value="pariwisata">Pariwisata & Hospitality</option>
                            <option value="lainnya">Lainnya</option>
                        </select>
                    </div>

                    <!-- Alamat Usaha - Lock ke Jawa Tengah, Purworejo -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-800 mb-3">Alamat Usaha</h4>
                        <div class="p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded mb-4">
                            <p class="text-sm text-yellow-800">
                                <strong>Catatan:</strong> Alamat usaha harus berada di wilayah Kabupaten Purworejo, Jawa Tengah.
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Provinsi</label>
                                <input type="text" value="Jawa Tengah" readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded bg-gray-100">
                                <input type="hidden" name="provinsi_usaha" value="33">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Kabupaten</label>
                                <input type="text" value="Purworejo" readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded bg-gray-100">
                                <input type="hidden" name="kabupaten_usaha" value="33.06">
                            </div>
                            <div>
                                <label for="kecamatan_usaha" class="block text-sm font-medium text-gray-700">Kecamatan *</label>
                                <select id="kecamatan_usaha" name="kecamatan_usaha" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Kecamatan</option>
                                </select>
                            </div>
                            <div>
                                <label for="desa_usaha" class="block text-sm font-medium text-gray-700">Desa/Kelurahan *</label>
                                <select id="desa_usaha" name="desa_usaha" required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Desa/Kelurahan</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="alamat_lengkap_usaha" class="block text-sm font-medium text-gray-700">Alamat Lengkap Usaha *</label>
                            <textarea id="alamat_lengkap_usaha" name="alamat_lengkap_usaha" rows="3" required
                                      class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Jalan, RT/RW, No. Rumah/Toko, dan detail alamat usaha"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Legalitas Usaha (Opsional) -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-600 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white w-6 h-6 rounded-full inline-flex items-center justify-center text-sm mr-2">3</span>
                        Legalitas Usaha (Opsional)
                    </h3>

                    <div class="p-4 bg-blue-50 border-l-4 border-blue-400 rounded mb-4">
                        <p class="text-sm text-blue-800">
                            <strong>Catatan:</strong> Bagian ini bersifat opsional. Anda dapat mengisi jika sudah memiliki dokumen legalitas usaha atau melengkapinya nanti.
                        </p>
                    </div>

                    <div class="space-y-4">
                        <!-- NIB -->
                        <div>
                            <label class="flex items-center space-x-2 font-medium text-gray-700">
                                <input type="checkbox" name="has_nib" id="has_nib" class="rounded">
                                <span>NIB (Nomor Induk Berusaha)</span>
                            </label>
                            <div id="nib_input" class="mt-2 hidden">
                                <input type="text" name="nib" placeholder="Masukkan Nomor Induk Berusaha"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- NPWP -->
                        <div>
                            <label class="flex items-center space-x-2 font-medium text-gray-700">
                                <input type="checkbox" name="has_npwp" id="has_npwp" class="rounded">
                                <span>NPWP (Nomor Pokok Wajib Pajak)</span>
                            </label>
                            <div id="npwp_input" class="mt-2 hidden">
                                <input type="text" name="npwp" placeholder="XX.XXX.XXX.X-XXX.XXX"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- Sertifikat Halal -->
                        <div>
                            <label class="flex items-center space-x-2 font-medium text-gray-700">
                                <input type="checkbox" name="has_sertifikat_halal" id="has_sertifikat_halal" class="rounded">
                                <span>Sertifikat Halal</span>
                            </label>
                            <div id="sertifikat_halal_input" class="mt-2 hidden">
                                <input type="text" name="sertifikat_halal" placeholder="Nomor Sertifikat Halal"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- SIUP -->
                        <div>
                            <label class="flex items-center space-x-2 font-medium text-gray-700">
                                <input type="checkbox" name="has_siup" id="has_siup" class="rounded">
                                <span>SIUP (Surat Izin Usaha Perdagangan)</span>
                            </label>
                            <div id="siup_input" class="mt-2 hidden">
                                <input type="text" name="siup" placeholder="Nomor SIUP"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- Legalitas Lainnya -->
                        <div>
                            <label class="flex items-center space-x-2 font-medium text-gray-700">
                                <input type="checkbox" name="has_legalitas_lainnya" id="has_legalitas_lainnya" class="rounded">
                                <span>Legalitas Lainnya</span>
                            </label>
                            <div id="legalitas_lainnya_input" class="mt-2 hidden">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Legalitas</label>
                                        <input type="text" name="nama_legalitas_lainnya" placeholder="Contoh: SITU, HO, Izin BPOM, dll"
                                               class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Nomor Legalitas</label>
                                        <input type="text" name="nomor_legalitas_lainnya" placeholder="Nomor dokumen legalitas"
                                               class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Keamanan Akun -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-600 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white w-6 h-6 rounded-full inline-flex items-center justify-center text-sm mr-2">4</span>
                        Keamanan Akun
                    </h3>

                    <div class="p-4 bg-blue-50 border-l-4 border-blue-400 rounded mb-4">
                        <p class="text-sm text-blue-800">
                            <strong>Catatan:</strong> Email yang Anda daftarkan akan digunakan sebagai username untuk login ke sistem Platform UMKM.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Password *</label>
                            <input id="password" name="password" type="password" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Minimal 8 karakter, kombinasi huruf dan angka">
                            <small class="text-gray-600 text-xs">Password harus mengandung minimal 8 karakter dengan kombinasi huruf dan angka</small>
                        </div>
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Konfirmasi Password *</label>
                            <input id="password_confirmation" name="password_confirmation" type="password" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Ulangi password yang sama">
                            <small class="text-gray-600 text-xs">Masukkan kembali password untuk konfirmasi</small>
                        </div>
                    </div>
                </div>

                <!-- Persetujuan -->
                <div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-400">
                    <h4 class="text-lg font-semibold text-yellow-800 mb-4">⚠️ Persetujuan dan Ketentuan</h4>

                    <div class="space-y-3">
                        <div class="flex items-start space-x-2">
                            <input type="checkbox" name="setuju_syarat" id="setuju_syarat" required class="mt-1 rounded">
                            <label for="setuju_syarat" class="text-sm text-gray-700">
                                Saya menyatakan bahwa semua data yang saya isi dalam formulir ini adalah benar dan dapat dipertanggungjawabkan secara hukum. Saya setuju dengan
                                <a href="#" class="text-blue-600 underline">syarat dan ketentuan</a> serta
                                <a href="#" class="text-blue-600 underline">kebijakan privasi</a> Platform UMKM.
                            </label>
                        </div>

                        <div class="flex items-start space-x-2">
                            <input type="checkbox" name="newsletter" id="newsletter" class="mt-1 rounded">
                            <label for="newsletter" class="text-sm text-gray-700">
                                Saya bersedia menerima informasi program, pelatihan, dan kegiatan Platform UMKM melalui email dan WhatsApp yang telah saya daftarkan.
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Tombol Submit -->
                <div class="text-center">
                    <button type="submit"
                            class="w-full max-w-md bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-4 px-8 rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition duration-300">
                        📝 Daftar UMKM Sekarang
                    </button>

                    <p class="mt-4 text-sm text-gray-600">
                        Sudah terdaftar sebagai UMKM?
                        <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">Masuk ke Dashboard</a>
                    </p>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Platform UMKM</h3>
                    <p class="text-gray-400">Membantu UMKM Indonesia berkembang dengan teknologi dan pelatihan terbaik</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Menu Utama</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/beranda" class="hover:text-white">Beranda</a></li>
                        <li><a href="/profil" class="hover:text-white">Profil</a></li>
                        <li><a href="/berita" class="hover:text-white">Berita</a></li>
                        <li><a href="/pelatihan" class="hover:text-white">Pelatihan</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Layanan</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/galeri" class="hover:text-white">Galeri</a></li>
                        <li><a href="/peta" class="hover:text-white">Peta UMKM</a></li>
                        <li><a href="/kontak" class="hover:text-white">Kontak</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Kontak</h4>
                    <div class="text-gray-400 space-y-2">
                        <p>Email: <EMAIL></p>
                        <p>Telepon: (021) 1234-5678</p>
                        <p>Alamat: Jakarta, Indonesia</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Platform UMKM. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    <script>
        // API Wilayah Configuration - Using Laravel WilayahController
        const WILAYAH_API = {
            base: '/api/wilayah',
            provinces: '/api/wilayah/provinces',
            regencies: '/api/wilayah/regencies',
            districts: '/api/wilayah/districts',
            villages: '/api/wilayah/villages'
        };

        // Loading state management
        function setLoading(selectElement, isLoading) {
            if (isLoading) {
                selectElement.innerHTML = '<option value="">Memuat...</option>';
                selectElement.disabled = true;
            } else {
                selectElement.disabled = false;
            }
        }

        // Clear dependent dropdowns
        function clearDependentDropdowns(level, type) {
            const dropdowns = {
                pribadi: ['kabupaten_pribadi', 'kecamatan_pribadi', 'desa_pribadi'],
                usaha: ['kecamatan_usaha', 'desa_usaha']
            };

            const startIndex = level === 'provinsi' ? 1 : level === 'kabupaten' ? 2 : level === 'kecamatan' ? 3 : 4;

            for (let i = startIndex; i < dropdowns[type].length; i++) {
                const element = document.getElementById(dropdowns[type][i]);
                if (element) {
                    element.innerHTML = '<option value="">Pilih ' + element.previousElementSibling.textContent.replace(' *', '') + '</option>';
                }
            }
        }

        // Fetch data from Laravel WilayahController
        async function fetchWilayahData(url) {
            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error('Network response was not ok');
                const result = await response.json();

                // Handle Laravel WilayahController response format
                if (result.data) {
                    return result.data;
                }

                // Handle direct API response format (fallback)
                if (result.status === 'success' && result.data) {
                    return result.data;
                }

                return [];
            } catch (error) {
                console.error('Error fetching data:', error);
                return [];
            }
        }

        // Load provinces for alamat pribadi (bebas seluruh Indonesia)
        async function loadProvinces() {
            const data = await fetchWilayahData(WILAYAH_API.provinces);

            const select = document.getElementById('provinsi_pribadi');
            select.innerHTML = '<option value="">Pilih Provinsi</option>';

            data.forEach(province => {
                const option = document.createElement('option');
                // Handle both formats: Laravel controller and direct API
                option.value = province.kode_wilayah || province.code;
                option.textContent = province.nama_wilayah || province.name;
                select.appendChild(option);
            });
        }

        // Load districts for Purworejo (33.06) - alamat usaha (lock)
        async function loadPurworejoDistricts() {
            const data = await fetchWilayahData(`${WILAYAH_API.districts}/33.06`);

            const select = document.getElementById('kecamatan_usaha');
            select.innerHTML = '<option value="">Pilih Kecamatan</option>';

            data.forEach(district => {
                const option = document.createElement('option');
                // Handle both formats: Laravel controller and direct API
                option.value = district.kode_wilayah || district.code;
                option.textContent = district.nama_wilayah || district.name;
                select.appendChild(option);
            });
        }

        // Load regencies for alamat pribadi
        async function loadRegencies(provinceCode, type) {
            const selectId = `kabupaten_${type}`;
            const select = document.getElementById(selectId);

            setLoading(select, true);
            clearDependentDropdowns('kabupaten', type);

            const data = await fetchWilayahData(`${WILAYAH_API.regencies}/${provinceCode}`);

            select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
            data.forEach(regency => {
                const option = document.createElement('option');
                // Handle both formats: Laravel controller and direct API
                option.value = regency.kode_wilayah || regency.code;
                option.textContent = regency.nama_wilayah || regency.name;
                select.appendChild(option);
            });

            setLoading(select, false);
        }

        // Load districts
        async function loadDistricts(regencyCode, type) {
            const selectId = `kecamatan_${type}`;
            const select = document.getElementById(selectId);

            setLoading(select, true);
            clearDependentDropdowns('kecamatan', type);

            const data = await fetchWilayahData(`${WILAYAH_API.districts}/${regencyCode}`);

            select.innerHTML = '<option value="">Pilih Kecamatan</option>';
            data.forEach(district => {
                const option = document.createElement('option');
                // Handle both formats: Laravel controller and direct API
                option.value = district.kode_wilayah || district.code;
                option.textContent = district.nama_wilayah || district.name;
                select.appendChild(option);
            });

            setLoading(select, false);
        }

        // Load villages
        async function loadVillages(districtCode, type) {
            const selectId = `desa_${type}`;
            const select = document.getElementById(selectId);

            setLoading(select, true);

            const data = await fetchWilayahData(`${WILAYAH_API.villages}/${districtCode}`);

            select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            data.forEach(village => {
                const option = document.createElement('option');
                // Handle both formats: Laravel controller and direct API
                option.value = village.kode_wilayah || village.code;
                option.textContent = village.nama_wilayah || village.name;
                select.appendChild(option);
            });

            setLoading(select, false);
        }

        // Toggle legalitas input fields
        function toggleLegalitasInput(type) {
            const checkbox = document.getElementById(`has_${type}`);
            const inputDiv = document.getElementById(`${type}_input`);

            if (checkbox.checked) {
                inputDiv.classList.remove('hidden');
            } else {
                inputDiv.classList.add('hidden');
                // Clear input values when hidden
                const inputs = inputDiv.querySelectorAll('input');
                inputs.forEach(input => input.value = '');
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Load initial data
            loadProvinces(); // For alamat pribadi (bebas seluruh Indonesia)
            loadPurworejoDistricts(); // For alamat usaha (lock ke Purworejo)

            // Legalitas checkbox handlers
            ['nib', 'npwp', 'sertifikat_halal', 'siup', 'legalitas_lainnya'].forEach(type => {
                const checkbox = document.getElementById(`has_${type}`);
                if (checkbox) {
                    checkbox.addEventListener('change', () => toggleLegalitasInput(type));
                }
            });

            // Province change handler for alamat pribadi
            document.getElementById('provinsi_pribadi').addEventListener('change', function() {
                if (this.value) {
                    loadRegencies(this.value, 'pribadi');
                } else {
                    clearDependentDropdowns('provinsi', 'pribadi');
                }
            });

            // Regency change handler for alamat pribadi
            document.getElementById('kabupaten_pribadi').addEventListener('change', function() {
                if (this.value) {
                    loadDistricts(this.value, 'pribadi');
                } else {
                    clearDependentDropdowns('kabupaten', 'pribadi');
                }
            });

            // District change handlers
            document.getElementById('kecamatan_pribadi').addEventListener('change', function() {
                if (this.value) {
                    loadVillages(this.value, 'pribadi');
                } else {
                    document.getElementById('desa_pribadi').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // District change handler for alamat usaha (lock ke Purworejo)
            document.getElementById('kecamatan_usaha').addEventListener('change', function() {
                if (this.value) {
                    loadVillages(this.value, 'usaha');
                } else {
                    document.getElementById('desa_usaha').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // Form validation
            document.getElementById('registrationForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Basic validation
                const requiredFields = [
                    'nama_lengkap', 'nik', 'email', 'nomor_hp',
                    'provinsi_pribadi', 'kabupaten_pribadi', 'kecamatan_pribadi', 'desa_pribadi', 'alamat_lengkap_pribadi',
                    'nama_usaha', 'bidang_usaha',
                    'kecamatan_usaha', 'desa_usaha', 'alamat_lengkap_usaha',
                    'password', 'password_confirmation'
                ];

                let isValid = true;
                requiredFields.forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field && !field.value.trim()) {
                        field.classList.add('border-red-500');
                        isValid = false;
                    } else if (field) {
                        field.classList.remove('border-red-500');
                    }
                });

                // Password confirmation check
                const password = document.querySelector('[name="password"]').value;
                const confirmPassword = document.querySelector('[name="password_confirmation"]').value;

                if (password !== confirmPassword) {
                    alert('Password dan konfirmasi password tidak sama!');
                    isValid = false;
                }

                // NIK validation (16 digits)
                const nik = document.querySelector('[name="nik"]').value;
                if (nik && nik.length !== 16) {
                    alert('NIK harus 16 digit!');
                    isValid = false;
                }

                // Terms agreement check
                const agreementChecked = document.querySelector('[name="setuju_syarat"]').checked;
                if (!agreementChecked) {
                    alert('Anda harus menyetujui syarat dan ketentuan!');
                    isValid = false;
                }

                if (isValid) {
                    alert('Form berhasil disubmit! (Ini hanya demo)');
                    // Here you would normally submit the form to your backend
                } else {
                    alert('Mohon lengkapi semua field yang wajib diisi!');
                }
            });
        });
    </script>
</body>
</html>
