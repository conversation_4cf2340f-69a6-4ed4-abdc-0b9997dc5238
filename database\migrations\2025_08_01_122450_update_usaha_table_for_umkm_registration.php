<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('usaha', function (Blueprint $table) {
            // Hapus kolom yang tidak diperlukan untuk registrasi UMKM
            $table->dropColumn([
                'jenis_usaha',
                'klasifikasi',
                'kelurahan',
                'kode_pos',
                'telepon',
                'email',
                'website',
                'status_verifikasi',
                'alasan_penolakan',
                'jumlah_karyawan',
                'omzet_bulanan',
                'modal_awal',
                'tahun_berdiri',
                'foto_usaha',
                'logo_usaha'
            ]);

            // Tambah kolom alamat usaha yang terstruktur
            $table->string('provinsi_usaha')->default('Jawa Tengah')->after('media_sosial');
            $table->string('kabupaten_usaha')->default('Purworejo')->after('provinsi_usaha');
            $table->string('kecamatan_usaha')->after('kabupaten_usaha');
            $table->string('desa_usaha')->nullable()->after('kecamatan_usaha');
            $table->text('alamat_lengkap_usaha')->after('desa_usaha');

            // Hapus kolom alamat lama yang duplikasi
            $table->dropColumn(['alamat_usaha', 'kecamatan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('usaha', function (Blueprint $table) {
            // Kembalikan kolom alamat lama
            $table->text('alamat_usaha')->after('nama_merk');
            $table->string('kecamatan')->after('deskripsi');

            // Hapus kolom alamat baru
            $table->dropColumn([
                'provinsi_usaha',
                'kabupaten_usaha',
                'kecamatan_usaha',
                'desa_usaha',
                'alamat_lengkap_usaha'
            ]);

            // Kembalikan kolom yang dihapus
            $table->string('jenis_usaha')->after('alamat_usaha');
            $table->string('klasifikasi')->after('bidang_usaha');
            $table->string('kelurahan')->nullable()->after('kecamatan');
            $table->string('kode_pos')->nullable()->after('kelurahan');
            $table->string('telepon')->nullable()->after('kode_pos');
            $table->string('email')->nullable()->after('telepon');
            $table->string('website')->nullable()->after('email');
            $table->enum('status_verifikasi', ['pending', 'approved', 'rejected'])->default('pending')->after('media_sosial');
            $table->text('alasan_penolakan')->nullable()->after('status_verifikasi');
            $table->integer('jumlah_karyawan')->nullable()->after('alasan_penolakan');
            $table->decimal('omzet_bulanan', 15, 2)->nullable()->after('jumlah_karyawan');
            $table->decimal('modal_awal', 15, 2)->nullable()->after('omzet_bulanan');
            $table->year('tahun_berdiri')->nullable()->after('modal_awal');
            $table->string('foto_usaha')->nullable()->after('tahun_berdiri');
            $table->string('logo_usaha')->nullable()->after('foto_usaha');
        });
    }
};
