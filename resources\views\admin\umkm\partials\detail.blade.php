<div class="row">
    <!-- Informasi Usaha -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light-primary">
                <h6 class="mb-0 text-primary">
                    <i class="bi bi-shop me-2"></i>Informasi Usaha
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="d-flex align-items-center gap-3 mb-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-primary text-white rounded">
                                    {{ strtoupper(substr(optional($umkm->usaha)->nama_usaha ?? $umkm->nama, 0, 2)) }}
                                </div>
                            </div>
                            <div>
                                <h5 class="mb-1">{{ optional($umkm->usaha)->nama_usaha ?? 'Belum diisi' }}</h5>
                                <p class="mb-0 text-muted small">Terdaftar: {{ $umkm->created_at->format('d M Y') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Nama Merk</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->nama_merk ?? 'Tidak ada merk' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Bidang Usaha</label>
                        <p class="mb-0">
                            @if(optional($umkm->usaha)->bidang_usaha)
                                @php
                                    $badgeClass = 'bg-light-primary text-primary';
                                    $bidangUsaha = optional($umkm->usaha)->bidang_usaha;
                                    switch($bidangUsaha) {
                                        case 'makanan_minuman':
                                            $badgeClass = 'bg-light-primary text-primary';
                                            $displayText = 'Makanan & Minuman';
                                            break;
                                        case 'kerajinan_tangan':
                                            $badgeClass = 'bg-light-warning text-warning';
                                            $displayText = 'Kerajinan Tangan';
                                            break;
                                        case 'perdagangan':
                                            $badgeClass = 'bg-light-info text-info';
                                            $displayText = 'Perdagangan';
                                            break;
                                        case 'jasa':
                                            $badgeClass = 'bg-light-danger text-danger';
                                            $displayText = 'Jasa';
                                            break;
                                        default:
                                            $displayText = ucfirst(str_replace('_', ' ', $bidangUsaha));
                                    }
                                @endphp
                                <span class="badge {{ $badgeClass }}">{{ $displayText }}</span>
                            @else
                                <span class="badge bg-light-secondary text-secondary">Belum diisi</span>
                            @endif
                        </p>
                    </div>
                    <div class="col-12">
                        <label class="form-label small text-muted">Deskripsi Usaha</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->deskripsi ?? 'Belum ada deskripsi' }}</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label small text-muted">Media Sosial</label>
                        <div>
                            @if(optional($umkm->usaha)->media_sosial)
                                @php
                                    $mediaSosialArray = explode('; ', optional($umkm->usaha)->media_sosial);
                                @endphp
                                @foreach($mediaSosialArray as $media)
                                    @if(!empty(trim($media)))
                                        @php
                                            $parts = explode(': ', $media, 2);
                                            $platform = $parts[0] ?? '';
                                            $account = $parts[1] ?? '';
                                        @endphp
                                        <p class="mb-1">
                                            <strong>{{ $platform }}:</strong> {{ $account }}
                                        </p>
                                    @endif
                                @endforeach
                            @else
                                <p class="mb-0 text-muted">Tidak ada media sosial</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informasi Pemilik -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light-success">
                <h6 class="mb-0 text-success">
                    <i class="bi bi-person me-2"></i>Informasi Pemilik
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Nama Lengkap</label>
                        <p class="mb-0">{{ $umkm->nama }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">NIK</label>
                        <p class="mb-0">{{ optional($umkm->profil)->nik ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Jenis Kelamin</label>
                        <p class="mb-0">{{ optional($umkm->profil)->jenis_kelamin ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Email</label>
                        <p class="mb-0">{{ $umkm->email }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">No. HP</label>
                        <p class="mb-0">{{ $umkm->no_hp ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label small text-muted">Alamat Pribadi</label>
                        <p class="mb-0">
                            @if($umkm->profil)
                                {{ $umkm->profil->alamat_lengkap }}, {{ $umkm->profil->desa }}, {{ $umkm->profil->kecamatan }}, {{ $umkm->profil->kabupaten }}, {{ $umkm->profil->provinsi }}
                            @else
                                Belum diisi
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alamat Usaha -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light-info">
                <h6 class="mb-0 text-info">
                    <i class="bi bi-geo-alt me-2"></i>Alamat Usaha
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Alamat Lengkap</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->alamat_lengkap_usaha ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Desa</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->desa_usaha ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Kecamatan</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->kecamatan_usaha ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Kabupaten</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->kabupaten_usaha ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-6">
                        <label class="form-label small text-muted">Provinsi</label>
                        <p class="mb-0">{{ optional($umkm->usaha)->provinsi_usaha ?? 'Belum diisi' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Legalitas & Dokumen -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light-warning">
                <h6 class="mb-0 text-warning">
                    <i class="bi bi-file-earmark-text me-2"></i>Legalitas & Dokumen
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Status Legalitas</label>
                        <div>
                            @if($umkm->legalitas && $umkm->legalitas->count() > 0)
                                @foreach($umkm->legalitas as $legal)
                                    <div class="mb-2">
                                        <span class="badge bg-light-success text-success me-2">{{ $legal->nama_legalitas }}</span>
                                        <small class="text-muted">{{ $legal->nomor_legalitas }}</small>
                                    </div>
                                @endforeach
                            @else
                                <span class="badge bg-light-danger text-danger">Belum Ada Legalitas</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informasi Tambahan -->
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light-secondary">
                <h6 class="mb-0 text-secondary">
                    <i class="bi bi-info-circle me-2"></i>Informasi Tambahan
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6 col-lg-3">
                        <label class="form-label small text-muted">Tanggal Daftar</label>
                        <p class="mb-0">{{ $umkm->created_at->format('d M Y H:i') }}</p>
                    </div>
                    <div class="col-6 col-lg-3">
                        <label class="form-label small text-muted">Status Akun</label>
                        <p class="mb-0">
                            <span class="badge bg-light-success text-success">Aktif</span>
                        </p>
                    </div>
                    <div class="col-6 col-lg-3">
                        <label class="form-label small text-muted">Terakhir Update</label>
                        <p class="mb-0">{{ $umkm->updated_at->format('d M Y H:i') }}</p>
                    </div>
                    <div class="col-6 col-lg-3">
                        <label class="form-label small text-muted">ID User</label>
                        <p class="mb-0">#{{ $umkm->id }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
