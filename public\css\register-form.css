/* Registration Form Styles */
.card {
    @apply bg-white shadow-lg rounded-lg p-8 border border-gray-200;
}

.card-body {
    @apply space-y-8;
}

.form-section {
    @apply border-b border-gray-200 pb-8 last:border-b-0;
}

.section-title {
    @apply flex items-center text-xl font-semibold text-gray-800 mb-6;
}

.step-indicator {
    @apply inline-flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium mr-3;
}

.step-indicator.active {
    @apply bg-blue-600;
}

.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200;
}

.form-input:focus {
    @apply ring-2 ring-blue-500 border-blue-500;
}

.form-input.bg-gray-100 {
    @apply bg-gray-100 cursor-not-allowed;
}

.form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 bg-white;
}

.form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-vertical;
}

.form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition duration-200;
}

.btn-primary {
    @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-primary:hover {
    @apply bg-blue-700 transform scale-105;
}

.alert {
    @apply p-4 rounded-md border;
}

.alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
}

.alert-danger {
    @apply bg-red-50 border-red-200 text-red-800;
}

/* Grid responsive */
.grid {
    @apply grid;
}

.grid-cols-1 {
    @apply grid-cols-1;
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        @apply grid-cols-2;
    }
}

.gap-4 {
    @apply gap-4;
}

/* Text colors for validation */
.text-red-500 {
    @apply text-red-500;
}

.text-green-500 {
    @apply text-green-500;
}

.text-yellow-500 {
    @apply text-yellow-500;
}

/* Loading states */
.loading {
    @apply opacity-50 pointer-events-none;
}

.spinner {
    @apply animate-spin inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full;
}

/* Form validation states */
.form-input.is-valid {
    @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}

.form-input.is-invalid {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.form-select.is-valid {
    @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}

.form-select.is-invalid {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

/* Responsive design */
@media (max-width: 640px) {
    .card {
        @apply p-4;
    }
    
    .section-title {
        @apply text-lg;
    }
    
    .step-indicator {
        @apply w-6 h-6 text-xs mr-2;
    }
}

/* Animation for form sections */
.form-section {
    @apply transition-all duration-300 ease-in-out;
}

.form-section:hover {
    @apply transform translate-y-0;
}

/* Focus states */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    @apply ring-2 ring-blue-500 border-blue-500 outline-none;
}

/* Disabled states */
.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
    @apply bg-gray-100 cursor-not-allowed opacity-60;
}

/* Required field indicator */
.form-label::after {
    content: '';
}

.form-label:has(+ input[required])::after,
.form-label:has(+ select[required])::after,
.form-label:has(+ textarea[required])::after {
    content: ' *';
    @apply text-red-500;
}
