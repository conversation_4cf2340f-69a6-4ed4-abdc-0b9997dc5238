@extends('layouts.admin-ltr')

@section('title', 'Kelo<PERSON>ri')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet" />
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page"><PERSON><PERSON><PERSON></li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGaleriModal">
                <i class="bi bi-plus-circle"></i> Upload Foto
            </button>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-end">
                <a class="dropdown-item" href="#"><i class="bi bi-upload"></i> Upload Bulk</a>
                <a class="dropdown-item" href="#"><i class="bi bi-download"></i> Download Semua</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#"><i class="bi bi-grid"></i> Tampilan Grid</a>
                <a class="dropdown-item" href="#"><i class="bi bi-list"></i> Tampilan List</a>
            </div>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-primary">
                        <p>Total Foto</p>
                        <h4 class="text-primary">1,247</h4>
                    </div>
                    <div class="w-50 bg-primary p-3">
                        <p class="mb-3 text-white">+ 45 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-success">
                        <p>Album</p>
                        <h4 class="text-success">24</h4>
                    </div>
                    <div class="w-50 bg-success p-3">
                        <p class="mb-3 text-white">+ 3 <i class="bi bi-arrow-up"></i></p>
                        <div id="chart2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-warning">
                        <p>Views Bulan Ini</p>
                        <h4 class="text-warning">15.6K</h4>
                    </div>
                    <div class="w-50 bg-warning p-3">
                        <p class="mb-3 text-white">+ 18% <i class="bi bi-arrow-up"></i></p>
                        <div id="chart3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-info">
                        <p>Storage Used</p>
                        <h4 class="text-info">2.4GB</h4>
                    </div>
                    <div class="w-50 bg-info p-3">
                        <p class="mb-3 text-white">+ 256MB <i class="bi bi-arrow-up"></i></p>
                        <div id="chart4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end row-->

<!-- Filter Section -->
<div class="card radius-10 mt-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-lg-3">
                <label class="form-label">Album</label>
                <select class="form-select" id="filterAlbum">
                    <option value="">Semua Album</option>
                    <option value="umkm">UMKM</option>
                    <option value="pelatihan">Pelatihan</option>
                    <option value="event">Event</option>
                    <option value="produk">Produk</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Tipe File</label>
                <select class="form-select" id="filterType">
                    <option value="">Semua Tipe</option>
                    <option value="jpg">JPG</option>
                    <option value="png">PNG</option>
                    <option value="gif">GIF</option>
                    <option value="webp">WebP</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Tanggal Upload</label>
                <input type="date" class="form-control" id="filterDate">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Cari</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Nama file..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gallery Grid -->
<div class="card radius-10 mt-4">
    <div class="card-header bg-transparent">
        <div class="row g-3 align-items-center">
            <div class="col">
                <h5 class="mb-0">Galeri Foto</h5>
            </div>
            <div class="col-auto">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-success btn-sm" onclick="publishSelected()">
                        <i class="bi bi-check-circle"></i> Publish Terpilih
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="moveToAlbum()">
                        <i class="bi bi-folder"></i> Pindah Album
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteSelected()">
                        <i class="bi bi-trash"></i> Hapus Terpilih
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <div class="gallery-item">
                    <div class="gallery-image">
                        <img src="{{ asset('ltr/assets/images/gallery/01.png') }}" alt="Gallery Image" class="img-fluid rounded">
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="btn btn-light btn-sm" onclick="viewImage(1)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="editImage(1)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="downloadImage(1)">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteImage(1)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                            <div class="gallery-checkbox">
                                <input type="checkbox" class="form-check-input gallery-select" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info mt-2">
                        <h6 class="mb-1">Pelatihan Digital Marketing</h6>
                        <p class="text-muted small mb-1">pelatihan-digital.jpg</p>
                        <div class="d-flex justify-content-between">
                            <span class="badge bg-light-primary text-primary">Pelatihan</span>
                            <small class="text-muted">2.4MB</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <div class="gallery-item">
                    <div class="gallery-image">
                        <img src="{{ asset('ltr/assets/images/gallery/02.png') }}" alt="Gallery Image" class="img-fluid rounded">
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="btn btn-light btn-sm" onclick="viewImage(2)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="editImage(2)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="downloadImage(2)">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteImage(2)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                            <div class="gallery-checkbox">
                                <input type="checkbox" class="form-check-input gallery-select" value="2">
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info mt-2">
                        <h6 class="mb-1">UMKM Warung Makan</h6>
                        <p class="text-muted small mb-1">warung-makan.jpg</p>
                        <div class="d-flex justify-content-between">
                            <span class="badge bg-light-success text-success">UMKM</span>
                            <small class="text-muted">1.8MB</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <div class="gallery-item">
                    <div class="gallery-image">
                        <img src="{{ asset('ltr/assets/images/gallery/03.png') }}" alt="Gallery Image" class="img-fluid rounded">
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="btn btn-light btn-sm" onclick="viewImage(3)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="editImage(3)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="downloadImage(3)">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteImage(3)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                            <div class="gallery-checkbox">
                                <input type="checkbox" class="form-check-input gallery-select" value="3">
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info mt-2">
                        <h6 class="mb-1">Event PLUT 2024</h6>
                        <p class="text-muted small mb-1">event-plut.jpg</p>
                        <div class="d-flex justify-content-between">
                            <span class="badge bg-light-warning text-warning">Event</span>
                            <small class="text-muted">3.2MB</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <div class="gallery-item">
                    <div class="gallery-image">
                        <img src="{{ asset('ltr/assets/images/gallery/04.png') }}" alt="Gallery Image" class="img-fluid rounded">
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="btn btn-light btn-sm" onclick="viewImage(4)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="editImage(4)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="downloadImage(4)">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteImage(4)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                            <div class="gallery-checkbox">
                                <input type="checkbox" class="form-check-input gallery-select" value="4">
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info mt-2">
                        <h6 class="mb-1">Produk Kerajinan</h6>
                        <p class="text-muted small mb-1">kerajinan-tangan.jpg</p>
                        <div class="d-flex justify-content-between">
                            <span class="badge bg-light-info text-info">Produk</span>
                            <small class="text-muted">1.5MB</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Mini charts
    var options1 = {
        series: [{
            name: 'Foto',
            data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
            height: 50,
            type: 'area',
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth'
        },
        fill: {
            opacity: 0.3,
        },
        colors: ['#ffffff'],
        tooltip: {
            enabled: false
        }
    };
    
    var chart1 = new ApexCharts(document.querySelector("#chart1"), options1);
    chart1.render();
    var chart2 = new ApexCharts(document.querySelector("#chart2"), options1);
    chart2.render();
    var chart3 = new ApexCharts(document.querySelector("#chart3"), options1);
    chart3.render();
    var chart4 = new ApexCharts(document.querySelector("#chart4"), options1);
    chart4.render();
});

function publishSelected() {
    var selected = $('.gallery-select:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih foto yang akan dipublish');
        return;
    }
    
    if (confirm('Publish ' + selected.length + ' foto yang dipilih?')) {
        console.log('Publishing:', selected);
    }
}

function moveToAlbum() {
    var selected = $('.gallery-select:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih foto yang akan dipindah');
        return;
    }
    
    // Show album selection modal
    console.log('Moving to album:', selected);
}

function deleteSelected() {
    var selected = $('.gallery-select:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('Pilih foto yang akan dihapus');
        return;
    }
    
    if (confirm('Hapus ' + selected.length + ' foto yang dipilih? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting:', selected);
    }
}

function viewImage(id) {
    console.log('Viewing image:', id);
    // Open image in modal or new tab
}

function editImage(id) {
    console.log('Editing image:', id);
    // Open image editor
}

function downloadImage(id) {
    console.log('Downloading image:', id);
    // Download image
}

function deleteImage(id) {
    if (confirm('Hapus foto ini? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting image:', id);
    }
}
</script>

<style>
.gallery-item {
    position: relative;
}

.gallery-image {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.gallery-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-actions {
    display: flex;
    gap: 8px;
}

.gallery-checkbox {
    position: absolute;
    top: 10px;
    left: 10px;
}

.gallery-info h6 {
    font-size: 14px;
    font-weight: 600;
}

.gallery-info p {
    font-size: 12px;
}
</style>
@endpush
