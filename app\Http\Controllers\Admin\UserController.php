<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Profil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index()
    {
        // Get all users with their profiles
        $users = User::with(['profil'])
            ->latest()
            ->get();

        // Statistics
        $totalUsers = User::count();
        $adminUsers = User::where('role', 'admin')->count();
        $umkmUsers = User::where('role', 'umkm')->count();
        $activeUsers = User::where('status', 'aktif')->count();
        $suspendedUsers = User::where('status', 'suspended')->count();
        $usersThisMonth = User::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        // Growth calculation (compare with last month)
        $lastMonthUsers = User::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        $growthPercentage = $lastMonthUsers > 0
            ? round((($usersThisMonth - $lastMonthUsers) / $lastMonthUsers) * 100, 1)
            : 0;

        return view('admin.users.index-ltr', compact(
            'users',
            'totalUsers',
            'adminUsers',
            'umkmUsers',
            'activeUsers',
            'suspendedUsers',
            'usersThisMonth',
            'growthPercentage'
        ));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'nama' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|string|min:8|confirmed',
                'no_hp' => 'nullable|string|max:20',
                'role' => 'required|in:admin,umkm',
                'foto_profil' => 'nullable|image|mimes:jpeg,jpg,png,gif|max:2048'
            ]);

            $user = User::create([
                'nama' => $request->nama,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'no_hp' => $request->no_hp,
                'role' => $request->role,
                'status' => 'aktif'
            ]);

            // Handle photo upload
            if ($request->hasFile('foto_profil')) {
                $foto = $request->file('foto_profil');
                $filename = 'profile_' . $user->id . '_' . time() . '.' . $foto->getClientOriginalExtension();
                $fotoPath = $foto->storeAs('profile_photos', $filename, 'public');

                // Create or update profil with photo
                $user->profil()->create([
                    'foto_profil' => $fotoPath
                ]);
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => ucfirst($request->role) . ' berhasil ditambahkan',
                    'user' => $user
                ]);
            }

            return redirect()->route('admin.users.index')
                ->with('success', ucfirst($request->role) . ' berhasil ditambahkan');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified user.
     */
    public function show($id)
    {
        $user = User::with(['profil', 'usaha', 'legalitas'])->findOrFail($id);
        return view('admin.users.show', compact('user'));
    }

    /**
     * Get user info for modal display.
     */
    public function getUserInfo($id)
    {
        try {
            $user = User::with(['profil'])->findOrFail($id);

            // Format data for response
            $userData = [
                'id' => $user->id,
                'name' => $user->nama ?? 'Tidak ada nama',
                'email' => $user->email,
                'role' => $user->role === 'admin' ? 'Administrator' : 'Pemilik UMKM',
                'status' => $user->status ?? 'aktif',
                'phone' => $user->no_hp ?? 'Tidak ada nomor',
                'address' => $user->profil ? $user->profil->full_address : 'Alamat belum diisi',
                'nik' => $user->profil->nik ?? 'NIK belum diisi',
                'gender' => $user->profil->jenis_kelamin ?? 'Tidak diisi',
                'joinDate' => $user->created_at ? $user->created_at->format('d M Y') : 'Tidak diketahui',
                'lastLogin' => $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Belum pernah login',
                'profilePicture' => asset('ltr/assets/images/avatars/avatar-' . (($user->id % 10) + 1) . '.png')
            ];

            return response()->json([
                'success' => true,
                'user' => $userData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'User tidak ditemukan'
            ], 404);
        }
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit($id)
    {
        // TODO: Implement user edit form
        return view('admin.users.edit', compact('id'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, $id)
    {
        // TODO: Implement user update logic
        return redirect()->route('admin.users.show', $id)
            ->with('success', 'Data pengguna berhasil diperbarui');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy($id)
    {
        // TODO: Implement user deletion logic
        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dihapus');
    }

    /**
     * Activate the specified user.
     */
    public function activate($id)
    {
        // TODO: Implement user activation logic
        return redirect()->back()
            ->with('success', 'Pengguna berhasil diaktifkan');
    }

    /**
     * Deactivate the specified user.
     */
    public function deactivate($id)
    {
        // TODO: Implement user deactivation logic
        return redirect()->back()
            ->with('success', 'Pengguna berhasil dinonaktifkan');
    }

    /**
     * Bulk activate selected users.
     */
    public function bulkActivate(Request $request)
    {
        $userIds = $request->input('user_ids', []);

        // TODO: Implement bulk activation logic

        return redirect()->back()
            ->with('success', count($userIds) . ' pengguna berhasil diaktifkan');
    }

    /**
     * Filter users with AJAX
     */
    public function filter(Request $request)
    {
        try {
            $query = User::with(['profil']);

            // Filter by role
            if ($request->filled('role')) {
                $query->where('role', $request->role);
            }

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Filter by date
            if ($request->filled('date')) {
                $query->whereDate('created_at', $request->date);
            }

            // Search by name or email
            if ($request->filled('search')) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('nama', 'like', "%{$searchTerm}%")
                      ->orWhere('email', 'like', "%{$searchTerm}%");
                });
            }

            $users = $query->latest()->get();

            // Generate HTML for table rows
            $html = '';
            foreach ($users as $user) {
                $html .= view('admin.users.partials.table-row', compact('user'))->render();
            }

            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $users->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
