<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Wilayah Indonesia</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Test API Wilayah Indonesia</h1>
        
        <!-- Test Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <button onclick="testProvinces()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Test Provinsi
            </button>
            <button onclick="testRegencies()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                Test Kabupaten (Jawa Tengah)
            </button>
            <button onclick="testDistricts()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                Test Kecamatan (Purworejo)
            </button>
            <button onclick="testVillages()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                Test Desa (Bagelen)
            </button>
        </div>

        <!-- Dropdown Test -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">Test Dropdown Cascade</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Provinsi</label>
                    <select id="test_provinsi" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Provinsi</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kabupaten/Kota</label>
                    <select id="test_kabupaten" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Kabupaten/Kota</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kecamatan</label>
                    <select id="test_kecamatan" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Kecamatan</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Desa/Kelurahan</label>
                    <select id="test_desa" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Desa/Kelurahan</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-bold mb-4">API Response</h2>
            <pre id="result" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
Klik tombol di atas untuk test API...
            </pre>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE = 'https://wilayah.id/api';
        
        // Display result function
        function displayResult(data, title) {
            const resultElement = document.getElementById('result');
            resultElement.textContent = `=== ${title} ===\n\n` + JSON.stringify(data, null, 2);
        }

        // Fetch data with error handling
        async function fetchData(url, title) {
            try {
                console.log(`Fetching: ${url}`);
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResult(data, title);
                
                // Return data for further processing
                if (data.status === 'success' && data.data) {
                    return data.data;
                }
                return [];
            } catch (error) {
                console.error('Error:', error);
                displayResult({
                    error: error.message,
                    url: url
                }, `ERROR - ${title}`);
                return [];
            }
        }

        // Test functions
        async function testProvinces() {
            const data = await fetchData(`${API_BASE}/provinces.json`, 'Test Provinsi');
            
            // Populate dropdown
            const select = document.getElementById('test_provinsi');
            select.innerHTML = '<option value="">Pilih Provinsi</option>';
            
            data.forEach(province => {
                const option = document.createElement('option');
                option.value = province.kode_wilayah;
                option.textContent = province.nama_wilayah;
                select.appendChild(option);
            });
        }

        async function testRegencies() {
            // Test dengan Jawa Tengah (kode: 33)
            await fetchData(`${API_BASE}/regencies/33.json`, 'Test Kabupaten - Jawa Tengah');
        }

        async function testDistricts() {
            // Test dengan Purworejo (kode: 33.06)
            await fetchData(`${API_BASE}/districts/33.06.json`, 'Test Kecamatan - Purworejo');
        }

        async function testVillages() {
            // Test dengan Bagelen (kode: 33.06.01)
            await fetchData(`${API_BASE}/villages/33.06.01.json`, 'Test Desa - Bagelen');
        }

        // Dropdown cascade functions
        async function loadRegencies(provinceCode) {
            const data = await fetchData(`${API_BASE}/regencies/${provinceCode}.json`, `Kabupaten - ${provinceCode}`);
            
            const select = document.getElementById('test_kabupaten');
            select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
            
            // Clear dependent dropdowns
            document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
            document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(regency => {
                const option = document.createElement('option');
                option.value = regency.kode_wilayah;
                option.textContent = regency.nama_wilayah;
                select.appendChild(option);
            });
        }

        async function loadDistricts(regencyCode) {
            const data = await fetchData(`${API_BASE}/districts/${regencyCode}.json`, `Kecamatan - ${regencyCode}`);
            
            const select = document.getElementById('test_kecamatan');
            select.innerHTML = '<option value="">Pilih Kecamatan</option>';
            
            // Clear dependent dropdown
            document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(district => {
                const option = document.createElement('option');
                option.value = district.kode_wilayah;
                option.textContent = district.nama_wilayah;
                select.appendChild(option);
            });
        }

        async function loadVillages(districtCode) {
            const data = await fetchData(`${API_BASE}/villages/${districtCode}.json`, `Desa - ${districtCode}`);
            
            const select = document.getElementById('test_desa');
            select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            
            data.forEach(village => {
                const option = document.createElement('option');
                option.value = village.kode_wilayah;
                option.textContent = village.nama_wilayah;
                select.appendChild(option);
            });
        }

        // Event listeners for dropdown cascade
        document.addEventListener('DOMContentLoaded', function() {
            // Load provinces on page load
            testProvinces();

            // Province change
            document.getElementById('test_provinsi').addEventListener('change', function() {
                if (this.value) {
                    loadRegencies(this.value);
                } else {
                    document.getElementById('test_kabupaten').innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
                    document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // Regency change
            document.getElementById('test_kabupaten').addEventListener('change', function() {
                if (this.value) {
                    loadDistricts(this.value);
                } else {
                    document.getElementById('test_kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });

            // District change
            document.getElementById('test_kecamatan').addEventListener('change', function() {
                if (this.value) {
                    loadVillages(this.value);
                } else {
                    document.getElementById('test_desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            });
        });

        // Console helper functions
        console.log('=== API Test Helper Functions ===');
        console.log('testProvinces() - Test load provinsi');
        console.log('testRegencies() - Test load kabupaten Jawa Tengah');
        console.log('testDistricts() - Test load kecamatan Purworejo');
        console.log('testVillages() - Test load desa Bagelen');
    </script>
</body>
</html>
