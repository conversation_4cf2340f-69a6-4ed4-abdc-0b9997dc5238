<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pelatihan_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pelatihan_id')->constrained('pelatihans')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('status', ['registered', 'confirmed', 'attended', 'completed', 'cancelled'])->default('registered');
            $table->timestamp('tanggal_daftar')->useCurrent();
            $table->timestamp('tanggal_konfirmasi')->nullable();
            $table->text('catatan')->nullable();
            $table->boolean('sertifikat_diterima')->default(false);
            $table->decimal('nilai', 5, 2)->nullable();
            $table->timestamps();

            $table->unique(['pelatihan_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pelatihan_user');
    }
};
