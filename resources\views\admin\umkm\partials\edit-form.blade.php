<div class="row">
    <!-- Data Pemilik -->
    <div class="mb-4 col-12">
        <h6 class="pb-2 text-primary border-bottom">
            <i class="bi bi-person me-2"></i>Data Pemilik
        </h6>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_nama" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_nama" name="nama" value="{{ $umkm->nama }}" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_email" class="form-label">Email <span class="text-danger">*</span></label>
            <input type="email" class="form-control" id="edit_email" name="email" value="{{ $umkm->email }}" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_no_hp" class="form-label">No. HP <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_no_hp" name="no_hp" value="{{ $umkm->no_hp }}" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_nik" class="form-label">NIK <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_nik" name="nik" value="{{ optional($umkm->profil)->nik }}" maxlength="16" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_jenis_kelamin" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_jenis_kelamin" name="jenis_kelamin" required>
                <option value="">Pilih Jenis Kelamin</option>
                <option value="Laki-laki" {{ optional($umkm->profil)->jenis_kelamin == 'Laki-laki' ? 'selected' : '' }}>Laki-laki</option>
                <option value="Perempuan" {{ optional($umkm->profil)->jenis_kelamin == 'Perempuan' ? 'selected' : '' }}>Perempuan</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_password" class="form-label">Password Baru</label>
            <input type="password" class="form-control" id="edit_password" name="password" placeholder="Kosongkan jika tidak ingin mengubah">
            <small class="text-muted">Kosongkan jika tidak ingin mengubah password</small>
        </div>
    </div>

    <!-- Alamat Pribadi -->
    <div class="mt-3 mb-4 col-12">
        <h6 class="pb-2 text-primary border-bottom">
            <i class="bi bi-house me-2"></i>Alamat Pribadi
        </h6>
    </div>
    <div class="col-md-3">
        <div class="mb-3">
            <label for="edit_provinsi" class="form-label">Provinsi <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_provinsi" name="provinsi" required>
                <option value="">Pilih Provinsi</option>
            </select>
        </div>
    </div>
    <div class="col-md-3">
        <div class="mb-3">
            <label for="edit_kabupaten" class="form-label">Kabupaten <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_kabupaten" name="kabupaten" required>
                <option value="">Pilih Kabupaten</option>
            </select>
        </div>
    </div>
    <div class="col-md-3">
        <div class="mb-3">
            <label for="edit_kecamatan" class="form-label">Kecamatan <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_kecamatan" name="kecamatan" required>
                <option value="">Pilih Kecamatan</option>
            </select>
        </div>
    </div>
    <div class="col-md-3">
        <div class="mb-3">
            <label for="edit_desa" class="form-label">Desa <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_desa" name="desa" required>
                <option value="">Pilih Desa</option>
            </select>
        </div>
    </div>
    <div class="col-12">
        <div class="mb-3">
            <label for="edit_alamat_lengkap" class="form-label">Alamat Lengkap <span class="text-danger">*</span></label>
            <textarea class="form-control" id="edit_alamat_lengkap" name="alamat_lengkap" rows="2" required>{{ optional($umkm->profil)->alamat_lengkap }}</textarea>
        </div>
    </div>

    <!-- Data Usaha -->
    <div class="mt-3 mb-4 col-12">
        <h6 class="pb-2 text-primary border-bottom">
            <i class="bi bi-shop me-2"></i>Data Usaha
        </h6>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_nama_usaha" class="form-label">Nama Usaha <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_nama_usaha" name="nama_usaha" value="{{ optional($umkm->usaha)->nama_usaha }}" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_nama_merk" class="form-label">Nama Merk</label>
            <input type="text" class="form-control" id="edit_nama_merk" name="nama_merk" value="{{ optional($umkm->usaha)->nama_merk }}">
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_bidang_usaha" class="form-label">Bidang Usaha <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_bidang_usaha" name="bidang_usaha" required>
                <option value="">Pilih Bidang Usaha</option>
                <option value="makanan_minuman" {{ optional($umkm->usaha)->bidang_usaha == 'makanan_minuman' ? 'selected' : '' }}>Makanan & Minuman</option>
                <option value="kerajinan_tangan" {{ optional($umkm->usaha)->bidang_usaha == 'kerajinan_tangan' ? 'selected' : '' }}>Kerajinan Tangan</option>
                <option value="perdagangan" {{ optional($umkm->usaha)->bidang_usaha == 'perdagangan' ? 'selected' : '' }}>Perdagangan</option>
                <option value="jasa" {{ optional($umkm->usaha)->bidang_usaha == 'jasa' ? 'selected' : '' }}>Jasa</option>
            </select>
        </div>
    </div>
    <div class="col-12">
        <div class="mb-3">
            <label class="form-label">Media Sosial</label>
            <div class="row g-2">
                @php
                    $mediaSosial = optional($umkm->usaha)->media_sosial ?? '';
                    $mediaSosialArray = explode('; ', $mediaSosial);
                    $mediaSosialData = [];
                    foreach ($mediaSosialArray as $media) {
                        if (!empty(trim($media))) {
                            $parts = explode(': ', $media, 2);
                            if (count($parts) == 2) {
                                $mediaSosialData[trim($parts[0])] = trim($parts[1]);
                            }
                        }
                    }
                @endphp
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_instagram_check" name="media_sosial_types[]" value="Instagram"
                               {{ isset($mediaSosialData['Instagram']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_instagram_check">Instagram</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_instagram_username" name="instagram_username"
                           placeholder="@username" value="{{ $mediaSosialData['Instagram'] ?? '' }}"
                           style="{{ isset($mediaSosialData['Instagram']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_facebook_check" name="media_sosial_types[]" value="Facebook"
                               {{ isset($mediaSosialData['Facebook']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_facebook_check">Facebook</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_facebook_username" name="facebook_username"
                           placeholder="Facebook Page/Profile" value="{{ $mediaSosialData['Facebook'] ?? '' }}"
                           style="{{ isset($mediaSosialData['Facebook']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_tiktok_check" name="media_sosial_types[]" value="TikTok"
                               {{ isset($mediaSosialData['TikTok']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_tiktok_check">TikTok</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_tiktok_username" name="tiktok_username"
                           placeholder="@username" value="{{ $mediaSosialData['TikTok'] ?? '' }}"
                           style="{{ isset($mediaSosialData['TikTok']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_whatsapp_check" name="media_sosial_types[]" value="WhatsApp"
                               {{ isset($mediaSosialData['WhatsApp']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_whatsapp_check">WhatsApp Business</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_whatsapp_number" name="whatsapp_number"
                           placeholder="08xxxxxxxxxx" value="{{ $mediaSosialData['WhatsApp'] ?? '' }}"
                           style="{{ isset($mediaSosialData['WhatsApp']) ? '' : 'display: none;' }}">
                </div>
            </div>
        </div>
    </div>
    <div class="col-12">
        <div class="mb-3">
            <label for="edit_deskripsi" class="form-label">Deskripsi Usaha</label>
            <textarea class="form-control" id="edit_deskripsi" name="deskripsi" rows="3">{{ optional($umkm->usaha)->deskripsi }}</textarea>
        </div>
    </div>

    <!-- Alamat Usaha -->
    <div class="mt-3 mb-4 col-12">
        <h6 class="pb-2 text-primary border-bottom">
            <i class="bi bi-geo-alt me-2"></i>Alamat Usaha
        </h6>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_kecamatan_usaha" class="form-label">Kecamatan <span class="text-danger">*</span></label>
            <select class="form-select" id="edit_kecamatan_usaha" name="kecamatan_usaha" required>
                <option value="">Pilih Kecamatan</option>
                <option value="Purwodadi" {{ optional($umkm->usaha)->kecamatan_usaha == 'Purwodadi' ? 'selected' : '' }}>Purwodadi</option>
                <option value="Bagelen" {{ optional($umkm->usaha)->kecamatan_usaha == 'Bagelen' ? 'selected' : '' }}>Bagelen</option>
                <option value="Kutoarjo" {{ optional($umkm->usaha)->kecamatan_usaha == 'Kutoarjo' ? 'selected' : '' }}>Kutoarjo</option>
                <option value="Grabag" {{ optional($umkm->usaha)->kecamatan_usaha == 'Grabag' ? 'selected' : '' }}>Grabag</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_desa_usaha" class="form-label">Desa <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_desa_usaha" name="desa_usaha" value="{{ optional($umkm->usaha)->desa_usaha }}" required>
        </div>
    </div>
    <div class="col-12">
        <div class="mb-3">
            <label for="edit_alamat_lengkap_usaha" class="form-label">Alamat Lengkap Usaha <span class="text-danger">*</span></label>
            <textarea class="form-control" id="edit_alamat_lengkap_usaha" name="alamat_lengkap_usaha" rows="2" required>{{ optional($umkm->usaha)->alamat_lengkap_usaha }}</textarea>
        </div>
    </div>

    <!-- Legalitas -->
    <div class="mt-3 mb-4 col-12">
        <h6 class="pb-2 text-primary border-bottom">
            <i class="bi bi-file-earmark-text me-2"></i>Legalitas (Opsional)
        </h6>
    </div>
    <div class="col-12">
        <div class="mb-3">
            <label class="form-label">Legalitas (Opsional)</label>
            <div class="row g-3">
                @php
                    $legalitasData = [];
                    if ($umkm->legalitas && $umkm->legalitas->count() > 0) {
                        foreach ($umkm->legalitas as $legal) {
                            $legalitasData[$legal->nama_legalitas] = $legal->nomor_legalitas;
                        }
                    }
                @endphp
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_nib_check" name="legalitas_types[]" value="NIB"
                               {{ isset($legalitasData['NIB']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_nib_check">NIB (Nomor Induk Berusaha)</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_nib_nomor" name="nib_nomor"
                           placeholder="Nomor NIB" value="{{ $legalitasData['NIB'] ?? '' }}"
                           style="{{ isset($legalitasData['NIB']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_siup_check" name="legalitas_types[]" value="SIUP"
                               {{ isset($legalitasData['SIUP']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_siup_check">SIUP (Surat Izin Usaha Perdagangan)</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_siup_nomor" name="siup_nomor"
                           placeholder="Nomor SIUP" value="{{ $legalitasData['SIUP'] ?? '' }}"
                           style="{{ isset($legalitasData['SIUP']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_sni_check" name="legalitas_types[]" value="SNI"
                               {{ isset($legalitasData['SNI']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_sni_check">SNI (Standar Nasional Indonesia)</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_sni_nomor" name="sni_nomor"
                           placeholder="Nomor SNI" value="{{ $legalitasData['SNI'] ?? '' }}"
                           style="{{ isset($legalitasData['SNI']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_halal_check" name="legalitas_types[]" value="HALAL"
                               {{ isset($legalitasData['HALAL']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_halal_check">Sertifikat Halal</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_halal_nomor" name="halal_nomor"
                           placeholder="Nomor Sertifikat Halal" value="{{ $legalitasData['HALAL'] ?? '' }}"
                           style="{{ isset($legalitasData['HALAL']) ? '' : 'display: none;' }}">
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_npwp_check" name="legalitas_types[]" value="NPWP"
                               {{ isset($legalitasData['NPWP']) ? 'checked' : '' }}>
                        <label class="form-check-label" for="edit_npwp_check">NPWP (Nomor Pokok Wajib Pajak)</label>
                    </div>
                    <input type="text" class="mt-2 form-control" id="edit_npwp_nomor" name="npwp_nomor"
                           placeholder="Nomor NPWP" value="{{ $legalitasData['NPWP'] ?? '' }}"
                           style="{{ isset($legalitasData['NPWP']) ? '' : 'display: none;' }}">
                </div>
            </div>

            <!-- Legalitas Lainnya -->
            <div class="mt-3">
                <button type="button" class="btn btn-outline-secondary btn-sm" id="edit-add-other-legalitas">
                    <i class="bi bi-plus-circle me-1"></i>Tambah Legalitas Lainnya
                </button>
            </div>
            <div id="edit-other-legalitas-container" class="mt-3">
                @php
                    $otherLegalitas = [];
                    $standardLegalitas = ['NIB', 'SIUP', 'SNI', 'HALAL', 'NPWP'];

                    if ($umkm->legalitas && $umkm->legalitas->count() > 0) {
                        foreach ($umkm->legalitas as $legal) {
                            if (!in_array($legal->nama_legalitas, $standardLegalitas)) {
                                $otherLegalitas[] = [
                                    'nama' => $legal->nama_legalitas,
                                    'nomor' => $legal->nomor_legalitas
                                ];
                            }
                        }
                    }
                @endphp
                @foreach($otherLegalitas as $index => $legal)
                    <div class="row g-2 mb-2 other-legalitas-item">
                        <div class="col-md-5">
                            <input type="text" class="form-control" name="other_legalitas_nama[]"
                                   placeholder="Nama Legalitas" value="{{ $legal['nama'] }}" required>
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control" name="other_legalitas_nomor[]"
                                   placeholder="Nomor Legalitas" value="{{ $legal['nomor'] }}" required>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-danger btn-sm remove-other-legalitas">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
