@extends('layouts.admin-ltr')

@section('title', '<PERSON><PERSON><PERSON>')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet" />
<style>
    /* Improved styling */
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .btn-group-sm > .btn, .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
    }
    
    .card-radius-10 {
        border-radius: 10px;
    }
    
    .pelatihan-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .pelatihan-desc {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .date-info {
        font-size: 0.85rem;
    }
    
    .time-info {
        font-size: 0.75rem;
    }
    
    .kuota-info {
        font-weight: 600;
    }
    
    .kuota-sisa {
        font-size: 0.75rem;
    }
    
    .notifikasi-info {
        font-size: 0.85rem;
    }
    
    .empty-state {
        padding: 2rem;
        text-align: center;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #6c757d;
    }
    
    .progress-thin {
        height: 3px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="mb-3 page-breadcrumb d-none d-sm-flex align-items-center">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="p-0 mb-0 breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Kelola Pelatihan</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a href="{{ route('admin.pelatihan.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle"></i> Tambah Pelatihan
            </a>
            <a href="{{ route('admin.pelatihan.notification-history') }}" class="btn btn-outline-info btn-sm">
                <i class="bi bi-bell"></i> Riwayat Notifikasi
            </a>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="mb-4 row row-cols-1 row-cols-md-2 row-cols-lg-4 g-3">
    <div class="col">
        <div class="card radius-10 bg-gradient-deepblue">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 text-white">{{ $pelatihans->where('status', 'open')->count() }}</h5>
                    <div class="ms-auto">
                        <i class="text-white bx bx-play-circle fs-3"></i>
                    </div>
                </div>
                <div class="my-3 progress bg-light-transparent progress-thin">
                    <div class="bg-white progress-bar" role="progressbar" style="width: 55%"></div>
                </div>
                <div class="text-white d-flex align-items-center">
                    <p class="mb-0">Pelatihan Aktif</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10 bg-gradient-orange">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 text-white">{{ $pelatihans->where('status', 'completed')->count() }}</h5>
                    <div class="ms-auto">
                        <i class="text-white bx bx-check-circle fs-3"></i>
                    </div>
                </div>
                <div class="my-3 progress bg-light-transparent progress-thin">
                    <div class="bg-white progress-bar" role="progressbar" style="width: 55%"></div>
                </div>
                <div class="text-white d-flex align-items-center">
                    <p class="mb-0">Pelatihan Selesai</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10 bg-gradient-ohhappiness">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 text-white">{{ $pelatihans->where('status', 'draft')->count() }}</h5>
                    <div class="ms-auto">
                        <i class="text-white bx bx-edit fs-3"></i>
                    </div>
                </div>
                <div class="my-3 progress bg-light-transparent progress-thin">
                    <div class="bg-white progress-bar" role="progressbar" style="width: 55%"></div>
                </div>
                <div class="text-white d-flex align-items-center">
                    <p class="mb-0">Draft</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10 bg-gradient-ibiza">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 text-white">{{ $pelatihans->sum('notifikasi_terkirim') }}</h5>
                    <div class="ms-auto">
                        <i class="text-white bx bx-bell fs-3"></i>
                    </div>
                </div>
                <div class="my-3 progress bg-light-transparent progress-thin">
                    <div class="bg-white progress-bar" role="progressbar" style="width: 55%"></div>
                </div>
                <div class="text-white d-flex align-items-center">
                    <p class="mb-0">Total Notifikasi</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pelatihan Table -->
<div class="card radius-10">
    <div class="bg-transparent card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Daftar Pelatihan</h5>
            <div class="d-flex align-items-center">
                <div class="input-group input-group-sm ms-2" style="width: 200px;">
                    <input type="text" class="form-control" placeholder="Cari pelatihan..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="pelatihanTable" class="table table-hover table-bordered" style="width:100%">
                <thead class="table-light">
                    <tr>
                        <th width="25%">Judul Pelatihan</th>
                        <th width="15%">Instruktur</th>
                        <th width="15%">Tanggal</th>
                        <th width="8%">Tipe</th>
                        <th width="8%">Kuota</th>
                        <th width="8%">Status</th>
                        <th width="10%">Notifikasi</th>
                        <th width="11%">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($pelatihans as $pelatihan)
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1 pelatihan-title">{{ $pelatihan->nama_pelatihan }}</h6>
                                <p class="mb-0 pelatihan-desc">{{ Str::limit($pelatihan->deskripsi, 50) }}</p>
                            </div>
                        </td>
                        <td>{{ $pelatihan->instruktur }}</td>
                        <td>
                            <div class="date-info">
                                <strong>{{ $pelatihan->tanggal_mulai->format('d M Y') }}</strong>
                                @if($pelatihan->tanggal_mulai != $pelatihan->tanggal_selesai)
                                    <br><small class="text-muted">s/d {{ $pelatihan->tanggal_selesai->format('d M Y') }}</small>
                                @endif
                                <br><small class="text-info time-info">{{ $pelatihan->jam_mulai->format('H:i') }} - {{ $pelatihan->jam_selesai->format('H:i') }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light-info text-info">{{ $pelatihan->tipe_text }}</span>
                        </td>
                        <td>
                            <div>
                                <span class="kuota-info">{{ $pelatihan->peserta_terdaftar }}/{{ $pelatihan->kuota }}</span>
                                <br><small class="text-muted kuota-sisa">Sisa: {{ $pelatihan->getAvailableSlots() }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ $pelatihan->status_badge }} status-badge">{{ $pelatihan->status_text }}</span>
                        </td>
                        <td>
                            @if($pelatihan->notifikasi_terkirim > 0)
                                <div class="notifikasi-info">
                                    <i class="bi bi-check-circle text-success"></i> {{ $pelatihan->notifikasi_terkirim }}
                                    <br><small class="text-muted">{{ $pelatihan->notifikasi_dikirim_at->diffForHumans() }}</small>
                                </div>
                            @else
                                <span class="text-muted">Belum dikirim</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex justify-content-center">
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('admin.pelatihan.show', $pelatihan) }}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="Lihat Detail">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.pelatihan.edit', $pelatihan) }}" class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" onclick="deletePelatihan({{ $pelatihan->id }})" data-bs-toggle="tooltip" title="Hapus">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="empty-state">
                                <i class="bi bi-inbox empty-state-icon"></i>
                                <h5 class="mb-2 text-muted">Belum ada pelatihan</h5>
                                <p class="mb-0 text-muted">Klik tombol "Tambah Pelatihan" untuk menambah pelatihan baru</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        @if($pelatihans->hasPages())
        <div class="mt-4 d-flex justify-content-center">
            {{ $pelatihans->links() }}
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#pelatihanTable').DataTable({
        searching: true,
        paging: false,
        info: false,
        responsive: true,
        language: {
            search: "",
            searchPlaceholder: "Cari pelatihan..."
        },
        initComplete: function() {
            // Move search input to header
            $('.dataTables_filter input').addClass('form-control form-control-sm');
            $('.dataTables_filter').appendTo('#pelatihanTable_wrapper .card-header');
            $('.dataTables_filter').css('margin-bottom', '0');
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function deletePelatihan(id) {
    Swal.fire({
        title: 'Hapus Pelatihan?',
        text: "Data pelatihan akan dihapus permanen!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/pelatihan/${id}`;
            
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            
            const tokenField = document.createElement('input');
            tokenField.type = 'hidden';
            tokenField.name = '_token';
            tokenField.value = '{{ csrf_token() }}';
            
            form.appendChild(methodField);
            form.appendChild(tokenField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
@endpush