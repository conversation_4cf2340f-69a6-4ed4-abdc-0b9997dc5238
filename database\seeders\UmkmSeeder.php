<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Profil;
use App\Models\Usaha;
use App\Models\Legalitas;
use Illuminate\Support\Facades\Hash;

class UmkmSeeder extends Seeder
{
    public function run()
    {
        $umkmData = [
            [
                'nama' => 'Siti Nurhaliza',
                'email' => '<EMAIL>',
                'no_hp' => '081234567890',
                'nama_usaha' => 'Warung Gudeg Bu Siti',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Purworejo',
                'desa' => 'Purworejo',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Ahmad Wijaya',
                'email' => '<EMAIL>',
                'no_hp' => '081234567891',
                'nama_usaha' => 'Kerajinan Bambu Wijaya',
                'bidang_usaha' => 'kerajinan_tangan',
                'kecamatan' => 'Bagelen',
                'desa' => 'Bagelen',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Dewi Sartika',
                'email' => '<EMAIL>',
                'no_hp' => '081234567892',
                'nama_usaha' => 'Toko Sembako Dewi',
                'bidang_usaha' => 'perdagangan',
                'kecamatan' => 'Banyuurip',
                'desa' => 'Banyuurip',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Budi Santoso',
                'email' => '<EMAIL>',
                'no_hp' => '081234567893',
                'nama_usaha' => 'Jasa Servis Motor Budi',
                'bidang_usaha' => 'jasa',
                'kecamatan' => 'Bruno',
                'desa' => 'Bruno',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Rina Kusuma',
                'email' => '<EMAIL>',
                'no_hp' => '081234567894',
                'nama_usaha' => 'Catering Rina',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Butuh',
                'desa' => 'Butuh',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Joko Susilo',
                'email' => '<EMAIL>',
                'no_hp' => '081234567895',
                'nama_usaha' => 'Anyaman Pandan Joko',
                'bidang_usaha' => 'kerajinan_tangan',
                'kecamatan' => 'Gebang',
                'desa' => 'Gebang',
                'verification_status' => 'rejected'
            ],
            [
                'nama' => 'Sri Wahyuni',
                'email' => '<EMAIL>',
                'no_hp' => '081234567896',
                'nama_usaha' => 'Toko Kelontong Sri',
                'bidang_usaha' => 'perdagangan',
                'kecamatan' => 'Grabag',
                'desa' => 'Grabag',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Agus Prasetyo',
                'email' => '<EMAIL>',
                'no_hp' => '081234567897',
                'nama_usaha' => 'Bengkel Las Agus',
                'bidang_usaha' => 'jasa',
                'kecamatan' => 'Kaligesing',
                'desa' => 'Kaligesing',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Lestari Indah',
                'email' => '<EMAIL>',
                'no_hp' => '081234567898',
                'nama_usaha' => 'Bakso Malang Lestari',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Kemiri',
                'desa' => 'Kemiri',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Hendra Gunawan',
                'email' => '<EMAIL>',
                'no_hp' => '081234567899',
                'nama_usaha' => 'Kerajinan Kayu Hendra',
                'bidang_usaha' => 'kerajinan_tangan',
                'kecamatan' => 'Loano',
                'desa' => 'Loano',
                'verification_status' => 'verified'
            ]
        ];

        // Add 10 more UMKM data
        $additionalData = [
            [
                'nama' => 'Maya Sari',
                'email' => '<EMAIL>',
                'no_hp' => '081234567800',
                'nama_usaha' => 'Warung Pecel Maya',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Ngombol',
                'desa' => 'Ngombol',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Rudi Hartono',
                'email' => '<EMAIL>',
                'no_hp' => '081234567801',
                'nama_usaha' => 'Toko Elektronik Rudi',
                'bidang_usaha' => 'perdagangan',
                'kecamatan' => 'Pituruh',
                'desa' => 'Pituruh',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Sari Dewi',
                'email' => '<EMAIL>',
                'no_hp' => '081234567802',
                'nama_usaha' => 'Salon Kecantikan Sari',
                'bidang_usaha' => 'jasa',
                'kecamatan' => 'Purwodadi',
                'desa' => 'Purwodadi',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Bambang Sutrisno',
                'email' => '<EMAIL>',
                'no_hp' => '081234567803',
                'nama_usaha' => 'Keripik Singkong Bambang',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Purworejo',
                'desa' => 'Purworejo',
                'verification_status' => 'rejected'
            ],
            [
                'nama' => 'Indira Sari',
                'email' => '<EMAIL>',
                'no_hp' => '081234567804',
                'nama_usaha' => 'Batik Tulis Indira',
                'bidang_usaha' => 'kerajinan_tangan',
                'kecamatan' => 'Bagelen',
                'desa' => 'Bagelen',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Wahyu Nugroho',
                'email' => '<EMAIL>',
                'no_hp' => '081234567805',
                'nama_usaha' => 'Toko Bangunan Wahyu',
                'bidang_usaha' => 'perdagangan',
                'kecamatan' => 'Banyuurip',
                'desa' => 'Banyuurip',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Fitri Handayani',
                'email' => '<EMAIL>',
                'no_hp' => '081234567806',
                'nama_usaha' => 'Laundry Fitri',
                'bidang_usaha' => 'jasa',
                'kecamatan' => 'Bruno',
                'desa' => 'Bruno',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Eko Prasetyo',
                'email' => '<EMAIL>',
                'no_hp' => '081234567807',
                'nama_usaha' => 'Soto Ayam Eko',
                'bidang_usaha' => 'makanan_minuman',
                'kecamatan' => 'Butuh',
                'desa' => 'Butuh',
                'verification_status' => 'verified'
            ],
            [
                'nama' => 'Nurul Hidayah',
                'email' => '<EMAIL>',
                'no_hp' => '081234567808',
                'nama_usaha' => 'Tas Rajut Nurul',
                'bidang_usaha' => 'kerajinan_tangan',
                'kecamatan' => 'Gebang',
                'desa' => 'Gebang',
                'verification_status' => 'pending'
            ],
            [
                'nama' => 'Doni Setiawan',
                'email' => '<EMAIL>',
                'no_hp' => '081234567809',
                'nama_usaha' => 'Minimarket Doni',
                'bidang_usaha' => 'perdagangan',
                'kecamatan' => 'Grabag',
                'desa' => 'Grabag',
                'verification_status' => 'pending'
            ]
        ];

        $allData = array_merge($umkmData, $additionalData);

        foreach ($allData as $data) {
            // Create User
            $user = User::create([
                'nama' => $data['nama'],
                'email' => $data['email'],
                'no_hp' => $data['no_hp'],
                'password' => Hash::make('password123'),
                'role' => 'umkm',
                'verification_status' => $data['verification_status'],
                'verified_at' => in_array($data['verification_status'], ['verified', 'rejected']) ? now() : null,
                'verified_by' => in_array($data['verification_status'], ['verified', 'rejected']) ? 1 : null,
            ]);

            // Create Profil
            Profil::create([
                'user_id' => $user->id,
                'nik' => '3304' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT) . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'jenis_kelamin' => rand(0, 1) ? 'Laki-laki' : 'Perempuan',
                'provinsi' => '33',
                'kabupaten' => '33.06',
                'kecamatan' => $data['kecamatan'],
                'desa' => $data['desa'],
                'alamat_lengkap' => 'Jl. ' . $data['nama'] . ' No. ' . rand(1, 100) . ', ' . $data['desa'] . ', ' . $data['kecamatan'] . ', Purworejo',
            ]);

            // Create Usaha
            Usaha::create([
                'user_id' => $user->id,
                'nama_usaha' => $data['nama_usaha'],
                'nama_merk' => $data['nama_usaha'],
                'bidang_usaha' => $data['bidang_usaha'],
                'deskripsi' => 'Usaha ' . $data['bidang_usaha'] . ' yang berkualitas dan terpercaya di ' . $data['kecamatan'],
                'provinsi_usaha' => '33',
                'kabupaten_usaha' => '33.06',
                'kecamatan_usaha' => $data['kecamatan'],
                'desa_usaha' => $data['desa'],
                'alamat_lengkap_usaha' => 'Jl. Usaha ' . $data['nama'] . ' No. ' . rand(1, 50) . ', ' . $data['desa'] . ', ' . $data['kecamatan'] . ', Purworejo',
            ]);

            // Create random Legalitas
            $legalitasTypes = ['NIB', 'SIUP', 'PIRT', 'HKI'];
            $randomLegalitas = array_rand($legalitasTypes, rand(1, 2));
            
            if (!is_array($randomLegalitas)) {
                $randomLegalitas = [$randomLegalitas];
            }

            foreach ($randomLegalitas as $index) {
                Legalitas::create([
                    'user_id' => $user->id,
                    'nama_legalitas' => $legalitasTypes[$index],
                    'nomor_legalitas' => $legalitasTypes[$index] . '-' . rand(100000, 999999) . '-' . date('Y'),
                ]);
            }
        }
    }
}
