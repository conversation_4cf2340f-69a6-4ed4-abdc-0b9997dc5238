<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test Form</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold mb-4">Test Form Submission</h2>
            
            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <form action="{{ route('auth.register.store') }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="nama" class="block text-sm font-medium text-gray-700">Nama</label>
                    <input type="text" id="nama" name="nama" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('nama') }}">
                </div>

                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" id="email" name="email" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('email') }}">
                </div>

                <div class="mb-4">
                    <label for="no_hp" class="block text-sm font-medium text-gray-700">No HP</label>
                    <input type="text" id="no_hp" name="no_hp" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('no_hp') }}">
                </div>

                <div class="mb-4">
                    <label for="nik" class="block text-sm font-medium text-gray-700">NIK</label>
                    <input type="text" id="nik" name="nik" required maxlength="16"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('nik') }}">
                </div>

                <div class="mb-4">
                    <label for="jenis_kelamin" class="block text-sm font-medium text-gray-700">Jenis Kelamin</label>
                    <select id="jenis_kelamin" name="jenis_kelamin" required 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="">Pilih</option>
                        <option value="Laki-laki" {{ old('jenis_kelamin') == 'Laki-laki' ? 'selected' : '' }}>Laki-laki</option>
                        <option value="Perempuan" {{ old('jenis_kelamin') == 'Perempuan' ? 'selected' : '' }}>Perempuan</option>
                    </select>
                </div>

                <!-- Alamat Pribadi -->
                <div class="mb-4">
                    <label for="provinsi" class="block text-sm font-medium text-gray-700">Provinsi</label>
                    <input type="text" id="provinsi" name="provinsi" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('provinsi', 'Jawa Tengah') }}">
                </div>

                <div class="mb-4">
                    <label for="kabupaten" class="block text-sm font-medium text-gray-700">Kabupaten</label>
                    <input type="text" id="kabupaten" name="kabupaten" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('kabupaten', 'Purworejo') }}">
                </div>

                <div class="mb-4">
                    <label for="kecamatan" class="block text-sm font-medium text-gray-700">Kecamatan</label>
                    <input type="text" id="kecamatan" name="kecamatan" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('kecamatan', 'Purworejo') }}">
                </div>

                <div class="mb-4">
                    <label for="desa" class="block text-sm font-medium text-gray-700">Desa</label>
                    <input type="text" id="desa" name="desa" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('desa', 'Purworejo') }}">
                </div>

                <div class="mb-4">
                    <label for="alamat_lengkap" class="block text-sm font-medium text-gray-700">Alamat Lengkap</label>
                    <textarea id="alamat_lengkap" name="alamat_lengkap" required 
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">{{ old('alamat_lengkap') }}</textarea>
                </div>

                <!-- Data Usaha -->
                <div class="mb-4">
                    <label for="nama_usaha" class="block text-sm font-medium text-gray-700">Nama Usaha</label>
                    <input type="text" id="nama_usaha" name="nama_usaha" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('nama_usaha') }}">
                </div>

                <div class="mb-4">
                    <label for="bidang_usaha" class="block text-sm font-medium text-gray-700">Bidang Usaha</label>
                    <select id="bidang_usaha" name="bidang_usaha" required 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="">Pilih</option>
                        <option value="makanan_minuman" {{ old('bidang_usaha') == 'makanan_minuman' ? 'selected' : '' }}>Makanan & Minuman</option>
                        <option value="kerajinan_tangan" {{ old('bidang_usaha') == 'kerajinan_tangan' ? 'selected' : '' }}>Kerajinan Tangan</option>
                        <option value="perdagangan" {{ old('bidang_usaha') == 'perdagangan' ? 'selected' : '' }}>Perdagangan</option>
                        <option value="jasa" {{ old('bidang_usaha') == 'jasa' ? 'selected' : '' }}>Jasa</option>
                    </select>
                </div>

                <!-- Alamat Usaha -->
                <div class="mb-4">
                    <label for="kecamatan_usaha" class="block text-sm font-medium text-gray-700">Kecamatan Usaha</label>
                    <input type="text" id="kecamatan_usaha" name="kecamatan_usaha" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('kecamatan_usaha', 'Purworejo') }}">
                </div>

                <div class="mb-4">
                    <label for="desa_usaha" class="block text-sm font-medium text-gray-700">Desa Usaha</label>
                    <input type="text" id="desa_usaha" name="desa_usaha" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                           value="{{ old('desa_usaha', 'Purworejo') }}">
                </div>

                <div class="mb-4">
                    <label for="alamat_lengkap_usaha" class="block text-sm font-medium text-gray-700">Alamat Lengkap Usaha</label>
                    <textarea id="alamat_lengkap_usaha" name="alamat_lengkap_usaha" required 
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">{{ old('alamat_lengkap_usaha') }}</textarea>
                </div>

                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" id="password" name="password" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>

                <div class="mb-4">
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Konfirmasi Password</label>
                    <input type="password" id="password_confirmation" name="password_confirmation" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>

                <button type="submit" class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600">
                    Daftar Test
                </button>
            </form>
        </div>
    </div>
</body>
</html>
