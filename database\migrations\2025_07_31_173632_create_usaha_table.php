<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usaha', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('nama_usaha');
            $table->string('nama_merk')->nullable();
            $table->text('alamat_usaha');
            $table->string('jenis_usaha'); // mikro, kecil, menengah
            $table->string('bidang_usaha'); // makanan_minuman, kerajinan_tangan, perdagangan, jasa
            $table->string('klasifikasi'); // industri, perdagangan, jasa
            $table->text('deskripsi')->nullable();
            $table->string('kecamatan');
            $table->string('kelurahan')->nullable();
            $table->string('kode_pos')->nullable();
            $table->string('telepon')->nullable();
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->string('media_sosial')->nullable();
            $table->enum('status_verifikasi', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('alasan_penolakan')->nullable();
            $table->integer('jumlah_karyawan')->nullable();
            $table->decimal('omzet_bulanan', 15, 2)->nullable();
            $table->decimal('modal_awal', 15, 2)->nullable();
            $table->year('tahun_berdiri')->nullable();
            $table->string('foto_usaha')->nullable();
            $table->string('logo_usaha')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usaha');
    }
};
