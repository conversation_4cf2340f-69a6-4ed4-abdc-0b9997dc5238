@extends('layouts.admin-ltr')

@section('title', 'Tambah Pelatihan')

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .form-group {
        margin-bottom: 1rem;
    }
    .required {
        color: red;
    }
    #editor {
        height: 200px;
    }
    .preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        margin-top: 10px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.pelatihan.index') }}">Pelatihan</a></li>
                <li class="breadcrumb-item active" aria-current="page">Tambah Pelatihan</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <a href="{{ route('admin.pelatihan.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Kembali
        </a>
    </div>
</div>
<!--end breadcrumb-->

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Tambah Pelatihan Baru</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.pelatihan.store') }}" method="POST" enctype="multipart/form-data" id="pelatihanForm">
                    @csrf
                    
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <!-- Basic Info -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Informasi Dasar</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="nama_pelatihan" class="form-label">Judul Pelatihan <span class="required">*</span></label>
                                        <input type="text" class="form-control @error('nama_pelatihan') is-invalid @enderror" 
                                               id="nama_pelatihan" name="nama_pelatihan" value="{{ old('nama_pelatihan') }}" required>
                                        @error('nama_pelatihan')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="deskripsi" class="form-label">Deskripsi Lengkap <span class="required">*</span></label>
                                        <div id="editor">{!! old('deskripsi') !!}</div>
                                        <input type="hidden" name="deskripsi" id="deskripsi">
                                        @error('deskripsi')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="instruktur" class="form-label">Instruktur <span class="required">*</span></label>
                                        <input type="text" class="form-control @error('instruktur') is-invalid @enderror" 
                                               id="instruktur" name="instruktur" value="{{ old('instruktur') }}" required>
                                        @error('instruktur')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="kategori" class="form-label">Kategori</label>
                                        <input type="text" class="form-control @error('kategori') is-invalid @enderror" 
                                               id="kategori" name="kategori" value="{{ old('kategori') }}" 
                                               placeholder="Contoh: Digital Marketing, Keuangan, dll">
                                        @error('kategori')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Schedule -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Jadwal Pelatihan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="tanggal_mulai" class="form-label">Tanggal Mulai <span class="required">*</span></label>
                                                <input type="date" class="form-control @error('tanggal_mulai') is-invalid @enderror" 
                                                       id="tanggal_mulai" name="tanggal_mulai" value="{{ old('tanggal_mulai') }}" required>
                                                @error('tanggal_mulai')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="tanggal_selesai" class="form-label">Tanggal Selesai <span class="required">*</span></label>
                                                <input type="date" class="form-control @error('tanggal_selesai') is-invalid @enderror" 
                                                       id="tanggal_selesai" name="tanggal_selesai" value="{{ old('tanggal_selesai') }}" required>
                                                @error('tanggal_selesai')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="jam_mulai" class="form-label">Jam Mulai <span class="required">*</span></label>
                                                <input type="time" class="form-control @error('jam_mulai') is-invalid @enderror" 
                                                       id="jam_mulai" name="jam_mulai" value="{{ old('jam_mulai') }}" required>
                                                @error('jam_mulai')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="jam_selesai" class="form-label">Jam Selesai <span class="required">*</span></label>
                                                <input type="time" class="form-control @error('jam_selesai') is-invalid @enderror" 
                                                       id="jam_selesai" name="jam_selesai" value="{{ old('jam_selesai') }}" required>
                                                @error('jam_selesai')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Location & Type -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Lokasi & Tipe Pelatihan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="tipe" class="form-label">Tipe Pelatihan <span class="required">*</span></label>
                                        <select class="form-select @error('tipe') is-invalid @enderror" id="tipe" name="tipe" required>
                                            <option value="">Pilih Tipe Pelatihan</option>
                                            <option value="offline" {{ old('tipe') == 'offline' ? 'selected' : '' }}>Offline</option>
                                            <option value="online" {{ old('tipe') == 'online' ? 'selected' : '' }}>Online</option>
                                            <option value="hybrid" {{ old('tipe') == 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                                        </select>
                                        @error('tipe')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group" id="lokasi-group">
                                        <label for="lokasi" class="form-label">Lokasi <span class="required">*</span></label>
                                        <input type="text" class="form-control @error('lokasi') is-invalid @enderror" 
                                               id="lokasi" name="lokasi" value="{{ old('lokasi') }}" 
                                               placeholder="Alamat lengkap tempat pelatihan">
                                        @error('lokasi')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group" id="link-group" style="display: none;">
                                        <label for="link_online" class="form-label">Link Online <span class="required">*</span></label>
                                        <input type="url" class="form-control @error('link_online') is-invalid @enderror" 
                                               id="link_online" name="link_online" value="{{ old('link_online') }}" 
                                               placeholder="https://zoom.us/j/123456789">
                                        @error('link_online')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Info -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Informasi Tambahan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="syarat_peserta" class="form-label">Syarat Peserta</label>
                                        <textarea class="form-control @error('syarat_peserta') is-invalid @enderror" 
                                                  id="syarat_peserta" name="syarat_peserta" rows="3" 
                                                  placeholder="Syarat dan ketentuan untuk mengikuti pelatihan">{{ old('syarat_peserta') }}</textarea>
                                        @error('syarat_peserta')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="materi" class="form-label">Materi yang Akan Dipelajari</label>
                                        <textarea class="form-control @error('materi') is-invalid @enderror" 
                                                  id="materi" name="materi" rows="4" 
                                                  placeholder="Outline materi pelatihan">{{ old('materi') }}</textarea>
                                        @error('materi')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-4">
                            <!-- Settings -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Pengaturan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="status" class="form-label">Status <span class="required">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="open" {{ old('status') == 'open' ? 'selected' : '' }}>Aktif (Buka Pendaftaran)</option>
                                            <option value="closed" {{ old('status') == 'closed' ? 'selected' : '' }}>Ditutup</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Pilih "Aktif" untuk mengirim notifikasi ke semua UMKM</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="kuota" class="form-label">Kuota Peserta <span class="required">*</span></label>
                                        <input type="number" class="form-control @error('kuota') is-invalid @enderror" 
                                               id="kuota" name="kuota" value="{{ old('kuota') }}" min="1" required>
                                        @error('kuota')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="biaya" class="form-label">Biaya Pelatihan <span class="required">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp</span>
                                            <input type="number" class="form-control @error('biaya') is-invalid @enderror" 
                                                   id="biaya" name="biaya" value="{{ old('biaya', 0) }}" min="0" required>
                                        </div>
                                        @error('biaya')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Masukkan 0 untuk pelatihan gratis</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Image -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Banner Pelatihan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="gambar" class="form-label">Upload Banner</label>
                                        <input type="file" class="form-control @error('gambar') is-invalid @enderror" 
                                               id="gambar" name="gambar" accept="image/*">
                                        @error('gambar')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                                        <div id="image-preview"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save"></i> Simpan Pelatihan
                                        </button>
                                        <a href="{{ route('admin.pelatihan.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-circle"></i> Batal
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Initialize Quill editor
var quill = new Quill('#editor', {
    theme: 'snow',
    modules: {
        toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline'],
            ['link'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['clean']
        ]
    }
});

// Handle form submission
document.getElementById('pelatihanForm').addEventListener('submit', function(e) {
    // Get Quill content and set to hidden input
    document.getElementById('deskripsi').value = quill.root.innerHTML;
});

// Handle tipe pelatihan change
document.getElementById('tipe').addEventListener('change', function() {
    const tipe = this.value;
    const lokasiGroup = document.getElementById('lokasi-group');
    const linkGroup = document.getElementById('link-group');
    const lokasiInput = document.getElementById('lokasi');
    const linkInput = document.getElementById('link_online');

    if (tipe === 'online') {
        lokasiGroup.style.display = 'none';
        linkGroup.style.display = 'block';
        lokasiInput.required = false;
        linkInput.required = true;
    } else if (tipe === 'hybrid') {
        lokasiGroup.style.display = 'block';
        linkGroup.style.display = 'block';
        lokasiInput.required = true;
        linkInput.required = true;
    } else {
        lokasiGroup.style.display = 'block';
        linkGroup.style.display = 'none';
        lokasiInput.required = true;
        linkInput.required = false;
    }
});

// Handle image preview
document.getElementById('gambar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('image-preview');
    
    if (file) {
        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            Swal.fire('Error', 'Ukuran file terlalu besar. Maksimal 2MB', 'error');
            this.value = '';
            preview.innerHTML = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="Preview">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Set minimum date to today
document.getElementById('tanggal_mulai').min = new Date().toISOString().split('T')[0];
document.getElementById('tanggal_selesai').min = new Date().toISOString().split('T')[0];

// Update tanggal_selesai minimum when tanggal_mulai changes
document.getElementById('tanggal_mulai').addEventListener('change', function() {
    document.getElementById('tanggal_selesai').min = this.value;
});
</script>
@endpush
