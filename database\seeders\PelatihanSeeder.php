<?php

namespace Database\Seeders;

use App\Models\Pelatihan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class PelatihanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pelatihans = [
            [
                'nama_pelatihan' => 'Digital Marketing untuk UMKM',
                'deskripsi' => '<p>Pelatihan komprehensif tentang digital marketing yang dirancang khusus untuk UMKM. Peserta akan mempelajari strategi pemasaran digital yang efektif, mulai dari social media marketing, content marketing, hingga online advertising.</p><p>Materi meliputi:</p><ul><li>Strategi Social Media Marketing</li><li>Content Creation dan Copywriting</li><li>Facebook dan Instagram Ads</li><li>Google My Business Optimization</li><li>Email Marketing</li></ul>',
                'instruktur' => '<PERSON><PERSON>, S.<PERSON>',
                'lokasi' => '<PERSON><PERSON>r <PERSON> dan UMKM Purworejo',
                'tanggal_mulai' => Carbon::now()->addDays(7),
                'tanggal_selesai' => Carbon::now()->addDays(9),
                'jam_mulai' => '08:00:00',
                'jam_selesai' => '16:00:00',
                'kuota' => 30,
                'peserta_terdaftar' => 15,
                'biaya' => 0,
                'kategori' => 'Digital Marketing',
                'status' => 'open',
                'tipe' => 'offline',
                'syarat_peserta' => 'Memiliki usaha UMKM yang sudah berjalan minimal 6 bulan, membawa laptop/smartphone, dan memiliki akun media sosial.',
                'materi' => 'Strategi Digital Marketing, Social Media Marketing, Content Marketing, Online Advertising, Analytics dan Reporting',
                'notifikasi_terkirim' => 25,
                'notifikasi_dikirim_at' => Carbon::now()->subDays(3),
                'email_terkirim' => true
            ],
            [
                'nama_pelatihan' => 'Manajemen Keuangan UMKM',
                'deskripsi' => '<p>Pelatihan manajemen keuangan yang akan membantu UMKM dalam mengelola keuangan usaha dengan lebih baik dan profesional.</p><p>Topik yang dibahas:</p><ul><li>Pembukuan sederhana</li><li>Laporan keuangan</li><li>Cash flow management</li><li>Analisis profitabilitas</li><li>Perencanaan keuangan</li></ul>',
                'instruktur' => 'Siti Nurhaliza, S.E., M.M',
                'lokasi' => 'Hotel Santika Purworejo',
                'tanggal_mulai' => Carbon::now()->addDays(14),
                'tanggal_selesai' => Carbon::now()->addDays(15),
                'jam_mulai' => '09:00:00',
                'jam_selesai' => '15:00:00',
                'kuota' => 25,
                'peserta_terdaftar' => 8,
                'biaya' => 150000,
                'kategori' => 'Keuangan',
                'status' => 'open',
                'tipe' => 'offline',
                'syarat_peserta' => 'Memiliki usaha UMKM, membawa kalkulator dan alat tulis, serta data keuangan usaha (jika ada).',
                'materi' => 'Dasar-dasar akuntansi, Pembukuan sederhana, Laporan keuangan, Analisis keuangan, Perencanaan budget'
            ],
            [
                'nama_pelatihan' => 'E-Commerce dan Marketplace',
                'deskripsi' => '<p>Pelatihan praktis tentang cara berjualan online melalui platform e-commerce dan marketplace populer di Indonesia.</p><p>Platform yang dibahas:</p><ul><li>Shopee</li><li>Tokopedia</li><li>Bukalapak</li><li>Lazada</li><li>Website sendiri</li></ul>',
                'instruktur' => 'Ahmad Fauzi, S.T',
                'lokasi' => 'Online via Zoom',
                'link_online' => 'https://zoom.us/j/123456789',
                'tanggal_mulai' => Carbon::now()->addDays(21),
                'tanggal_selesai' => Carbon::now()->addDays(21),
                'jam_mulai' => '19:00:00',
                'jam_selesai' => '21:00:00',
                'kuota' => 50,
                'peserta_terdaftar' => 32,
                'biaya' => 0,
                'kategori' => 'E-Commerce',
                'status' => 'open',
                'tipe' => 'online',
                'syarat_peserta' => 'Memiliki smartphone/laptop dengan koneksi internet stabil, memiliki produk yang akan dijual online.',
                'materi' => 'Pengenalan marketplace, Cara membuat toko online, Optimasi produk, Customer service online, Strategi promosi'
            ],
            [
                'nama_pelatihan' => 'Fotografi Produk untuk UMKM',
                'deskripsi' => '<p>Belajar teknik fotografi produk yang menarik untuk meningkatkan daya jual produk UMKM di media sosial dan marketplace.</p><p>Yang akan dipelajari:</p><ul><li>Teknik dasar fotografi</li><li>Pencahayaan produk</li><li>Komposisi foto</li><li>Editing foto sederhana</li><li>Foto untuk media sosial</li></ul>',
                'instruktur' => 'Rina Wijaya, S.Sn',
                'lokasi' => 'Studio Foto Creative Purworejo',
                'tanggal_mulai' => Carbon::now()->subDays(7),
                'tanggal_selesai' => Carbon::now()->subDays(7),
                'jam_mulai' => '10:00:00',
                'jam_selesai' => '16:00:00',
                'kuota' => 20,
                'peserta_terdaftar' => 20,
                'biaya' => 100000,
                'kategori' => 'Fotografi',
                'status' => 'completed',
                'tipe' => 'offline',
                'syarat_peserta' => 'Membawa smartphone/kamera, membawa 2-3 produk untuk dipraktikkan.',
                'materi' => 'Dasar fotografi, Teknik pencahayaan, Komposisi foto, Editing dengan smartphone, Tips foto produk'
            ],
            [
                'nama_pelatihan' => 'Branding dan Packaging UMKM',
                'deskripsi' => '<p>Pelatihan tentang pentingnya branding dan packaging yang menarik untuk meningkatkan nilai jual produk UMKM.</p><p>Materi pelatihan:</p><ul><li>Konsep branding</li><li>Desain logo sederhana</li><li>Packaging yang menarik</li><li>Brand identity</li><li>Marketing materials</li></ul>',
                'instruktur' => 'Dedi Kurniawan, S.Ds',
                'lokasi' => 'Balai Desa Purworejo',
                'link_online' => 'https://meet.google.com/abc-defg-hij',
                'tanggal_mulai' => Carbon::now()->addDays(28),
                'tanggal_selesai' => Carbon::now()->addDays(29),
                'jam_mulai' => '13:00:00',
                'jam_selesai' => '17:00:00',
                'kuota' => 35,
                'peserta_terdaftar' => 12,
                'biaya' => 75000,
                'kategori' => 'Branding',
                'status' => 'open',
                'tipe' => 'hybrid',
                'syarat_peserta' => 'Memiliki produk UMKM, membawa contoh kemasan produk saat ini (jika ada).',
                'materi' => 'Konsep branding, Desain logo, Packaging design, Brand guidelines, Marketing collateral'
            ],
            [
                'nama_pelatihan' => 'Legalitas dan Perizinan UMKM',
                'deskripsi' => '<p>Pelatihan tentang berbagai aspek legalitas dan perizinan yang diperlukan untuk menjalankan usaha UMKM secara legal dan profesional.</p><p>Topik yang dibahas:</p><ul><li>NIB (Nomor Induk Berusaha)</li><li>SIUP dan TDP</li><li>NPWP dan PKP</li><li>PIRT dan Halal</li><li>Hak Kekayaan Intelektual</li></ul>',
                'instruktur' => 'Bambang Sutrisno, S.H., M.H',
                'lokasi' => 'Kantor Dinas Perindustrian dan Perdagangan Purworejo',
                'tanggal_mulai' => Carbon::now()->addDays(35),
                'tanggal_selesai' => Carbon::now()->addDays(35),
                'jam_mulai' => '08:30:00',
                'jam_selesai' => '12:00:00',
                'kuota' => 40,
                'peserta_terdaftar' => 5,
                'biaya' => 0,
                'kategori' => 'Legalitas',
                'status' => 'open',
                'tipe' => 'offline',
                'syarat_peserta' => 'Memiliki usaha UMKM, membawa dokumen identitas dan dokumen usaha yang sudah ada.',
                'materi' => 'Pengenalan legalitas usaha, Cara mengurus NIB, Perizinan usaha, Pajak UMKM, Perlindungan HKI'
            ],
            [
                'nama_pelatihan' => 'Customer Service Excellence',
                'deskripsi' => '<p>Pelatihan untuk meningkatkan kualitas pelayanan pelanggan yang akan berdampak pada loyalitas dan kepuasan customer.</p><p>Materi meliputi:</p><ul><li>Komunikasi efektif</li><li>Handling complaint</li><li>Customer retention</li><li>Service recovery</li><li>Building customer loyalty</li></ul>',
                'instruktur' => 'Maya Sari, S.Psi., M.M',
                'lokasi' => 'Online via Zoom',
                'link_online' => 'https://zoom.us/j/987654321',
                'tanggal_mulai' => Carbon::now()->subDays(14),
                'tanggal_selesai' => Carbon::now()->subDays(14),
                'jam_mulai' => '19:30:00',
                'jam_selesai' => '21:30:00',
                'kuota' => 60,
                'peserta_terdaftar' => 45,
                'biaya' => 0,
                'kategori' => 'Customer Service',
                'status' => 'completed',
                'tipe' => 'online',
                'syarat_peserta' => 'Memiliki usaha yang berinteraksi langsung dengan pelanggan.',
                'materi' => 'Komunikasi pelanggan, Menangani keluhan, Membangun loyalitas, Service excellence, Customer experience'
            ],
            [
                'nama_pelatihan' => 'Strategi Pemasaran Offline',
                'deskripsi' => '<p>Pelatihan tentang strategi pemasaran konvensional yang masih efektif untuk UMKM di era digital.</p><p>Strategi yang dibahas:</p><ul><li>Word of mouth marketing</li><li>Event marketing</li><li>Partnership dan kolaborasi</li><li>Promosi offline</li><li>Community building</li></ul>',
                'instruktur' => 'Joko Widodo, S.E',
                'lokasi' => 'Gedung Serbaguna Purworejo',
                'tanggal_mulai' => Carbon::now()->addDays(42),
                'tanggal_selesai' => Carbon::now()->addDays(42),
                'jam_mulai' => '14:00:00',
                'jam_selesai' => '18:00:00',
                'kuota' => 30,
                'peserta_terdaftar' => 0,
                'biaya' => 50000,
                'kategori' => 'Marketing',
                'status' => 'draft',
                'tipe' => 'offline',
                'syarat_peserta' => 'Memiliki usaha UMKM yang sudah berjalan.',
                'materi' => 'Marketing offline, Event planning, Partnership strategy, Community engagement, Promotional tactics'
            ]
        ];

        foreach ($pelatihans as $pelatihan) {
            Pelatihan::create($pelatihan);
        }
    }
}
