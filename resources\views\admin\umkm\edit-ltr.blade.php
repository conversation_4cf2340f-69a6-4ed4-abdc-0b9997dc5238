@extends('layouts.admin-ltr')

@section('title', 'Edit UMKM')

@section('content')
<!-- Breadcrumb -->
<div class="mb-3 page-breadcrumb d-none d-sm-flex align-items-center">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="p-0 mb-0 breadcrumb">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item"><a href="/admin/umkm">Kelola UMKM</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit UMKM</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <button type="submit" form="editUmkmForm" class="btn btn-primary">
                <i class="bi bi-check-circle"></i> Simpan Perubahan
            </button>
            <a href="/admin/umkm/1" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i> Batal
            </a>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<form id="editUmkmForm" action="/admin/umkm/1" method="POST" enctype="multipart/form-data">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-12 col-lg-8">
            <!-- Basic Info -->
            <div class="mb-4 card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Informasi Dasar</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Nama UMKM <span class="text-danger">*</span></label>
                            <input type="text" name="nama_umkm" value="Warung Makan Sederhana" class="form-control" required>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Kategori <span class="text-danger">*</span></label>
                            <select name="kategori" class="form-select" required>
                                <option value="kuliner" selected>Kuliner</option>
                                <option value="fashion">Fashion</option>
                                <option value="kerajinan">Kerajinan</option>
                                <option value="teknologi">Teknologi</option>
                                <option value="lainnya">Lainnya</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Tahun Berdiri</label>
                            <input type="number" name="tahun_berdiri" value="2020" class="form-control" min="1900" max="2024">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Jumlah Karyawan</label>
                            <input type="number" name="jumlah_karyawan" value="5" class="form-control" min="1">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Deskripsi</label>
                            <textarea name="deskripsi" rows="3" class="form-control">Warung makan yang menyediakan berbagai macam makanan tradisional Indonesia dengan cita rasa yang autentik dan harga yang terjangkau. Kami berkomitmen untuk memberikan pelayanan terbaik kepada pelanggan.</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="mb-4 card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Informasi Kontak</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Nama Pemilik <span class="text-danger">*</span></label>
                            <input type="text" name="nama_pemilik" value="Budi Santoso" class="form-control" required>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" name="email" value="<EMAIL>" class="form-control" required>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Nomor Telepon</label>
                            <input type="tel" name="telepon" value="+62 812-3456-7890" class="form-control">
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">WhatsApp</label>
                            <input type="tel" name="whatsapp" value="+62 812-3456-7890" class="form-control">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Alamat</label>
                            <textarea name="alamat" rows="2" class="form-control">Jl. Ahmad Yani No. 123, Purworejo Kota, Kabupaten Purworejo, Jawa Tengah 54111</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Info -->
            <div class="mb-4 card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Informasi Usaha</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <label class="form-label">Modal Awal</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" name="modal_awal" value="50000000" class="form-control">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Omzet Bulanan</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" name="omzet_bulanan" value="25000000" class="form-control">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Aset</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" name="aset" value="75000000" class="form-control">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <label class="form-label">Status Tempat Usaha</label>
                            <select name="status_tempat" class="form-select">
                                <option value="milik_sendiri" selected>Milik Sendiri</option>
                                <option value="sewa">Sewa</option>
                                <option value="kontrak">Kontrak</option>
                                <option value="lainnya">Lainnya</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location -->
            <div class="card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Lokasi</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12 col-md-4">
                            <label class="form-label">Provinsi</label>
                            <select name="provinsi" id="provinsi" class="form-select">
                                <option value="33" selected>Jawa Tengah</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-4">
                            <label class="form-label">Kabupaten/Kota</label>
                            <select name="kabupaten" id="kabupaten" class="form-select">
                                <option value="3318" selected>Kabupaten Purworejo</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-4">
                            <label class="form-label">Kecamatan</label>
                            <select name="kecamatan" id="kecamatan" class="form-select">
                                <option value="331801" selected>Purworejo</option>
                                <option value="331802">Bagelen</option>
                                <option value="331803">Kutoarjo</option>
                                <option value="331804">Grabag</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-12 col-lg-4">
            <!-- Status & Verification -->
            <div class="mb-4 card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Status & Verifikasi</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Status Verifikasi</label>
                        <select name="status_verifikasi" class="form-select">
                            <option value="pending">Menunggu Verifikasi</option>
                            <option value="verified" selected>Terverifikasi</option>
                            <option value="rejected">Ditolak</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status Aktif</label>
                        <select name="status_aktif" class="form-select">
                            <option value="1" selected>Aktif</option>
                            <option value="0">Nonaktif</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">Catatan Admin</label>
                        <textarea name="catatan_admin" rows="3" class="form-control" placeholder="Catatan untuk UMKM ini..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Upload Documents -->
            <div class="mb-4 card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Dokumen</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">KTP Pemilik</label>
                        <input type="file" name="ktp" accept=".pdf,.jpg,.jpeg,.png" class="form-control">
                        <div class="form-text">File saat ini: ktp-budi-santoso.pdf</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">SIUP</label>
                        <input type="file" name="siup" accept=".pdf,.jpg,.jpeg,.png" class="form-control">
                        <div class="form-text">File saat ini: siup-warung-makan.pdf</div>
                    </div>
                    <div>
                        <label class="form-label">Foto Usaha</label>
                        <input type="file" name="foto_usaha" accept="image/*" class="form-control">
                        <div class="form-text">File saat ini: foto-warung.jpg</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card radius-10">
                <div class="bg-transparent card-header">
                    <h5 class="mb-0">Aksi Cepat</h5>
                </div>
                <div class="card-body">
                    <div class="gap-2 d-grid">
                        <button type="button" class="btn btn-success" onclick="approveAndSave()">
                            <i class="bi bi-check-circle"></i> Setujui & Simpan
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectAndSave()">
                            <i class="bi bi-x-circle"></i> Tolak & Simpan
                        </button>
                        <button type="button" class="btn btn-primary" onclick="sendNotification()">
                            <i class="bi bi-bell"></i> Kirim Notifikasi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
function approveAndSave() {
    document.querySelector('select[name="status_verifikasi"]').value = 'verified';
    document.getElementById('editUmkmForm').submit();
}

function rejectAndSave() {
    document.querySelector('select[name="status_verifikasi"]').value = 'rejected';
    document.getElementById('editUmkmForm').submit();
}

function sendNotification() {
    // Open modal or redirect to notification page
    alert('Fitur kirim notifikasi akan segera tersedia');
}

// Form validation
document.getElementById('editUmkmForm').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Mohon lengkapi semua field yang wajib diisi');
    }
});
</script>
@endpush
