@extends('layouts.admin-ltr')

@section('title', 'Detail Pelatihan')

@push('styles')
<style>
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    .info-value {
        color: #212529;
        margin-bottom: 15px;
    }
    .status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.pelatihan.index') }}">Pelatihan</a></li>
                <li class="breadcrumb-item active" aria-current="page">Detail Pelatihan</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a href="{{ route('admin.pelatihan.edit', $pelatihan) }}" class="btn btn-warning">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <a href="{{ route('admin.pelatihan.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Info -->
    <div class="col-lg-8">
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="mb-1">{{ $pelatihan->nama_pelatihan }}</h4>
                        <p class="text-muted mb-0">{{ $pelatihan->kategori ?? 'Umum' }}</p>
                    </div>
                    <span class="badge bg-{{ $pelatihan->status_badge }} status-badge">
                        {{ $pelatihan->status_text }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-label">Instruktur</div>
                        <div class="info-value">{{ $pelatihan->instruktur }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-label">Tipe Pelatihan</div>
                        <div class="info-value">
                            <span class="badge bg-light-info text-info">{{ $pelatihan->tipe_text }}</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="info-label">Tanggal Mulai</div>
                        <div class="info-value">{{ $pelatihan->tanggal_mulai->format('d M Y') }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-label">Tanggal Selesai</div>
                        <div class="info-value">{{ $pelatihan->tanggal_selesai->format('d M Y') }}</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="info-label">Jam Mulai</div>
                        <div class="info-value">{{ $pelatihan->jam_mulai->format('H:i') }} WIB</div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-label">Jam Selesai</div>
                        <div class="info-value">{{ $pelatihan->jam_selesai->format('H:i') }} WIB</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="info-label">Lokasi</div>
                        <div class="info-value">{{ $pelatihan->lokasi }}</div>
                    </div>
                    @if($pelatihan->link_online)
                    <div class="col-md-6">
                        <div class="info-label">Link Online</div>
                        <div class="info-value">
                            <a href="{{ $pelatihan->link_online }}" target="_blank" class="text-primary">
                                {{ $pelatihan->link_online }}
                            </a>
                        </div>
                    </div>
                    @endif
                </div>

                <div class="info-label">Deskripsi</div>
                <div class="info-value">{!! $pelatihan->deskripsi !!}</div>

                @if($pelatihan->syarat_peserta)
                <div class="info-label">Syarat Peserta</div>
                <div class="info-value">{{ $pelatihan->syarat_peserta }}</div>
                @endif

                @if($pelatihan->materi)
                <div class="info-label">Materi</div>
                <div class="info-value">{{ $pelatihan->materi }}</div>
                @endif
            </div>
        </div>
    </div>

    <!-- Side Info -->
    <div class="col-lg-4">
        <!-- Stats -->
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <h6 class="mb-0">Statistik Pelatihan</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ $pelatihan->kuota }}</h4>
                        <small class="text-muted">Kuota</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $pelatihan->peserta_terdaftar }}</h4>
                        <small class="text-muted">Terdaftar</small>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between">
                        <span>Sisa Kuota</span>
                        <span class="fw-bold">{{ $pelatihan->getAvailableSlots() }}</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar" style="width: {{ ($pelatihan->peserta_terdaftar / $pelatihan->kuota) * 100 }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Biaya -->
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <h6 class="mb-0">Biaya Pelatihan</h6>
            </div>
            <div class="card-body text-center">
                @if($pelatihan->biaya > 0)
                    <h3 class="text-primary">Rp {{ number_format($pelatihan->biaya, 0, ',', '.') }}</h3>
                @else
                    <h3 class="text-success">GRATIS</h3>
                @endif
            </div>
        </div>

        <!-- Notifikasi -->
        @if($pelatihan->notifikasi_terkirim > 0)
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <h6 class="mb-0">Notifikasi</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle text-success fs-4 me-3"></i>
                    <div>
                        <div class="fw-bold">{{ $pelatihan->notifikasi_terkirim }} notifikasi terkirim</div>
                        <small class="text-muted">{{ $pelatihan->notifikasi_dikirim_at->diffForHumans() }}</small>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Banner -->
        @if($pelatihan->gambar)
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <h6 class="mb-0">Banner Pelatihan</h6>
            </div>
            <div class="card-body p-0">
                <img src="{{ asset('storage/' . $pelatihan->gambar) }}" class="img-fluid rounded-bottom" alt="Banner">
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Peserta -->
@if($pelatihan->users->count() > 0)
<div class="row mt-4">
    <div class="col-12">
        <div class="card info-card">
            <div class="card-header bg-transparent">
                <h6 class="mb-0">Daftar Peserta ({{ $pelatihan->users->count() }})</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Tanggal Daftar</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($pelatihan->users as $user)
                            <tr>
                                <td>{{ $user->nama }}</td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    <span class="badge bg-light-success text-success">
                                        {{ $user->pivot->status ?? 'Terdaftar' }}
                                    </span>
                                </td>
                                <td>{{ $user->pivot->created_at->format('d M Y') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
