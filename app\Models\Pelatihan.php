<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Pelatihan extends Model
{
    protected $fillable = [
        'nama_pelatihan',
        'deskripsi',
        'instruktur',
        'lokasi',
        'tanggal_mulai',
        'tanggal_selesai',
        'jam_mulai',
        'jam_selesai',
        'kuota',
        'peserta_terdaftar',
        'biaya',
        'kategori',
        'status',
        'syarat_peserta',
        'materi',
        'sertifikat',
        'gambar',
        'link_online',
        'tipe',
        'notifikasi_terkirim',
        'notifikasi_dikirim_at',
        'email_terkirim'
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'jam_mulai' => 'datetime:H:i',
        'jam_selesai' => 'datetime:H:i',
        'kuota' => 'integer',
        'peserta_terdaftar' => 'integer',
        'biaya' => 'decimal:2',
        'notifikasi_dikirim_at' => 'datetime',
        'email_terkirim' => 'boolean'
    ];

    /**
     * Get the users that belong to the pelatihan.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'pelatihan_user')
                    ->withPivot([
                        'status',
                        'tanggal_daftar',
                        'tanggal_konfirmasi',
                        'catatan',
                        'sertifikat_diterima',
                        'nilai'
                    ])
                    ->withTimestamps();
    }

    /**
     * Scope for open pelatihans.
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Check if pelatihan is full.
     */
    public function isFull(): bool
    {
        return $this->peserta_terdaftar >= $this->kuota;
    }

    /**
     * Get available slots.
     */
    public function getAvailableSlots(): int
    {
        return max(0, $this->kuota - $this->peserta_terdaftar);
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'secondary',
            'open' => 'success',
            'closed' => 'warning',
            'completed' => 'primary',
            'cancelled' => 'danger'
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    /**
     * Get status text in Indonesian
     */
    public function getStatusTextAttribute()
    {
        $texts = [
            'draft' => 'Draft',
            'open' => 'Aktif',
            'closed' => 'Ditutup',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan'
        ];

        return $texts[$this->status] ?? 'Draft';
    }

    /**
     * Get tipe text in Indonesian
     */
    public function getTipeTextAttribute()
    {
        $texts = [
            'offline' => 'Offline',
            'online' => 'Online',
            'hybrid' => 'Hybrid'
        ];

        return $texts[$this->tipe] ?? 'Offline';
    }

    /**
     * Get complete location info
     */
    public function getLokasiLengkapAttribute()
    {
        if ($this->tipe === 'online') {
            return $this->link_online ? 'Online: ' . $this->link_online : 'Online';
        } elseif ($this->tipe === 'hybrid') {
            $lokasi = $this->lokasi;
            if ($this->link_online) {
                $lokasi .= ' / Online: ' . $this->link_online;
            }
            return $lokasi;
        }

        return $this->lokasi;
    }

    /**
     * Scope for active pelatihans
     */
    public function scopeAktif($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Scope for completed pelatihans
     */
    public function scopeSelesai($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for closed pelatihans
     */
    public function scopeDitutup($query)
    {
        return $query->where('status', 'closed');
    }
}
