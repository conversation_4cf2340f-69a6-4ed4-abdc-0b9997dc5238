@extends('layouts.admin-ltr')

@section('title', 'Kelola Pengguna')

@push('styles')
<link href="{{ asset('ltr/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet" />
<style>
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
    }

    .table-responsive::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    #usersTable th, #usersTable td {
        white-space: nowrap;
    }

    .d-flex.gap-1.flex-nowrap {
        flex-wrap: nowrap !important;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Kelola Pengguna</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
            <i class="bi bi-plus-circle"></i> Tambah Admin
        </button>
    </div>
</div>
<!--end breadcrumb-->

<!-- Stats Cards -->
<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-4">
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-primary">
                        <p>Total Pengguna</p>
                        <h4 class="text-primary">{{ number_format($totalUsers) }}</h4>
                    </div>
                    <div class="w-50 bg-primary p-3">
                        <p class="mb-3 text-white">
                            @if($growthPercentage >= 0)
                                + {{ $growthPercentage }}% <i class="bi bi-arrow-up"></i>
                            @else
                                {{ $growthPercentage }}% <i class="bi bi-arrow-down"></i>
                            @endif
                        </p>
                        <div id="chart1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-success">
                        <p>Pemilik UMKM</p>
                        <h4 class="text-success">{{ number_format($umkmUsers) }}</h4>
                    </div>
                    <div class="w-50 bg-success p-3">
                        <p class="mb-3 text-white">
                            {{ $totalUsers > 0 ? round(($umkmUsers / $totalUsers) * 100, 1) : 0 }}% <i class="bi bi-shop"></i>
                        </p>
                        <div id="chart2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-warning">
                        <p>Administrator</p>
                        <h4 class="text-warning">{{ number_format($adminUsers) }}</h4>
                    </div>
                    <div class="w-50 bg-warning p-3">
                        <p class="mb-3 text-white">
                            {{ $totalUsers > 0 ? round(($adminUsers / $totalUsers) * 100, 1) : 0 }}% <i class="bi bi-shield-check"></i>
                        </p>
                        <div id="chart3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card overflow-hidden radius-10">
            <div class="card-body p-2">
                <div class="d-flex align-items-stretch justify-content-between radius-10 overflow-hidden">
                    <div class="w-50 p-3 bg-light-info">
                        <p>Pengguna Aktif</p>
                        <h4 class="text-info">{{ number_format($activeUsers) }}</h4>
                    </div>
                    <div class="w-50 bg-info p-3">
                        <p class="mb-3 text-white">
                            {{ $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) : 0 }}% <i class="bi bi-check-circle"></i>
                        </p>
                        <div id="chart4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end row-->

<!-- Filter Section -->
<div class="card radius-10 mt-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-12 col-lg-3">
                <label class="form-label">Role</label>
                <select class="form-select" id="filterRole">
                    <option value="">Semua Role</option>
                    <option value="admin">Administrator</option>
                    <option value="umkm">Pemilik UMKM</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="filterStatus">
                    <option value="">Semua Status</option>
                    <option value="aktif">Aktif</option>
                    <option value="nonaktif">Nonaktif</option>
                    <option value="suspended">Suspended</option>
                </select>
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Tanggal Daftar</label>
                <input type="date" class="form-control" id="filterDate">
            </div>
            <div class="col-12 col-lg-3">
                <label class="form-label">Cari</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Nama atau email..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card radius-10 mt-4">
    <div class="card-header bg-transparent">
        <div class="row g-3 align-items-center">
            <div class="col">
                <h5 class="mb-0">Daftar Pengguna</h5>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive" style="overflow-x: auto; max-width: 100%;">
            <table id="usersTable" class="table table-striped table-bordered" style="min-width: 1000px; width: 100%;">
                <thead>
                    <tr>
                        <th style="min-width: 250px; width: 250px;">Pengguna</th>
                        <th style="width: 120px; min-width: 120px;">Role</th>
                        <th style="width: 100px; min-width: 100px;">Status</th>
                        <th style="width: 150px; min-width: 150px;">Terakhir Login</th>
                        <th style="width: 120px; min-width: 120px;">Tanggal Daftar</th>
                        <th style="width: 250px; min-width: 250px;">Aksi</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    @forelse($users as $user)
                    <tr data-role="{{ $user->role }}" data-status="{{ $user->status ?? 'aktif' }}">
                        <td>
                            <div class="d-flex align-items-center gap-3">
                                <div class="product-box border">
                                    @if($user->profil && $user->profil->foto_profil)
                                        <img src="{{ asset('storage/' . $user->profil->foto_profil) }}" alt="Foto Profil" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                    @else
                                        <img src="{{ asset('ltr/assets/images/avatars/avatar-' . (($user->id % 10) + 1) . '.png') }}" alt="Avatar Default" class="rounded-circle" width="40" height="40">
                                    @endif
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name mb-1">{{ $user->nama ?? 'Nama belum diisi' }}</h6>
                                    <p class="mb-0 product-category text-secondary">{{ $user->email }}</p>
                                    @if($user->no_hp)
                                        <small class="text-muted"><i class="bi bi-phone"></i> {{ $user->no_hp }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            @if($user->role === 'admin')
                                <span class="badge bg-light-danger text-danger">Administrator</span>
                            @else
                                <span class="badge bg-light-primary text-primary">Pemilik UMKM</span>
                            @endif
                        </td>
                        <td>
                            @php
                                $status = $user->status ?? 'aktif';
                            @endphp
                            @if($status === 'aktif')
                                <span class="badge bg-light-success text-success">Aktif</span>
                            @elseif($status === 'suspended')
                                <span class="badge bg-light-warning text-warning">Suspended</span>
                            @else
                                <span class="badge bg-light-secondary text-secondary">Nonaktif</span>
                            @endif
                        </td>
                        <td>
                            @if($user->last_login_at)
                                {{ $user->last_login_at->diffForHumans() }}
                            @else
                                Belum pernah login
                            @endif
                        </td>
                        <td>{{ $user->created_at->format('d M Y') }}</td>
                        <td>
                            <div class="d-flex gap-1 flex-nowrap">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="showUserInfo({{ $user->id }})" data-bs-toggle="tooltip" title="Info Pribadi">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning" onclick="editUser({{ $user->id }})" data-bs-toggle="tooltip" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                @if($user->status === 'aktif' || !$user->status)
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="suspendUser({{ $user->id }})" data-bs-toggle="tooltip" title="Suspend">
                                        <i class="bi bi-pause"></i>
                                    </button>
                                @else
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="activateUser({{ $user->id }})" data-bs-toggle="tooltip" title="Aktifkan">
                                        <i class="bi bi-play"></i>
                                    </button>
                                @endif
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ $user->id }})" data-bs-toggle="tooltip" title="Hapus">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                            Belum ada data pengguna yang terdaftar
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Informasi Pribadi -->
<div class="modal fade" id="userInfoModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userInfoModalLabel">Informasi Pribadi Pengguna</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="userInfoModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('ltr/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
<script src="{{ asset('ltr/assets/plugins/apexcharts-bundle/js/apexcharts.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<meta name="csrf-token" content="{{ csrf_token() }}">

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#usersTable').DataTable({
        "pageLength": 10,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-checkbox').prop('checked', this.checked);
    });

    // Mini charts
    var options1 = {
        series: [{
            name: 'Users',
            data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
            height: 50,
            type: 'area',
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth'
        },
        fill: {
            opacity: 0.3,
        },
        colors: ['#ffffff'],
        tooltip: {
            enabled: false
        }
    };

    var chart1 = new ApexCharts(document.querySelector("#chart1"), options1);
    chart1.render();
    var chart2 = new ApexCharts(document.querySelector("#chart2"), options1);
    chart2.render();
    var chart3 = new ApexCharts(document.querySelector("#chart3"), options1);
    chart3.render();
    var chart4 = new ApexCharts(document.querySelector("#chart4"), options1);
    chart4.render();
});

function activateSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selected.length === 0) {
        alert('Pilih pengguna yang akan diaktifkan');
        return;
    }

    if (confirm('Aktifkan ' + selected.length + ' pengguna yang dipilih?')) {
        console.log('Activating:', selected);
    }
}

function suspendSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selected.length === 0) {
        alert('Pilih pengguna yang akan di-suspend');
        return;
    }

    if (confirm('Suspend ' + selected.length + ' pengguna yang dipilih?')) {
        console.log('Suspending:', selected);
    }
}

function deleteSelected() {
    var selected = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selected.length === 0) {
        alert('Pilih pengguna yang akan dihapus');
        return;
    }

    if (confirm('Hapus ' + selected.length + ' pengguna yang dipilih? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting:', selected);
    }
}

function deleteUser(id) {
    if (confirm('Hapus pengguna ini? Tindakan ini tidak dapat dibatalkan.')) {
        console.log('Deleting user:', id);
    }
}

function showUserInfo(userId) {
    // Show loading state
    $('#userInfoModalBody').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Memuat informasi pengguna...</p>
        </div>
    `);

    // Show modal
    $('#userInfoModal').modal('show');

    // AJAX call to get user info
    $.ajax({
        url: `/admin/users/${userId}/info`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const userInfo = generateUserInfoHTML(response.user);
                $('#userInfoModalBody').html(userInfo);
            } else {
                $('#userInfoModalBody').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#userInfoModalBody').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> Terjadi kesalahan saat memuat data pengguna
                </div>
            `);
        }
    });
}

function generateUserInfoHTML(user) {
    return `
        <div class="row">
            <div class="col-md-4 text-center">
                <img src="${user.profilePicture}" alt="Profile Picture" class="rounded-circle mb-3" width="120" height="120">
                <h5>${user.name}</h5>
                <span class="badge ${user.status === 'aktif' ? 'bg-success' : user.status === 'suspended' ? 'bg-warning' : 'bg-secondary'}">${user.status.charAt(0).toUpperCase() + user.status.slice(1)}</span>
            </div>
            <div class="col-md-8">
                <table class="table table-borderless">
                    <tr>
                        <td width="30%"><strong>Nama Lengkap:</strong></td>
                        <td>${user.name}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>${user.email}</td>
                    </tr>
                    <tr>
                        <td><strong>Role:</strong></td>
                        <td><span class="badge ${user.role === 'Administrator' ? 'bg-danger' : 'bg-primary'}">${user.role}</span></td>
                    </tr>
                    <tr>
                        <td><strong>No. Telepon:</strong></td>
                        <td>${user.phone}</td>
                    </tr>
                    <tr>
                        <td><strong>NIK:</strong></td>
                        <td>${user.nik}</td>
                    </tr>
                    <tr>
                        <td><strong>Jenis Kelamin:</strong></td>
                        <td>${user.gender}</td>
                    </tr>
                    <tr>
                        <td><strong>Alamat:</strong></td>
                        <td>${user.address}</td>
                    </tr>
                    <tr>
                        <td><strong>Tanggal Bergabung:</strong></td>
                        <td>${user.joinDate}</td>
                    </tr>
                    <tr>
                        <td><strong>Terakhir Login:</strong></td>
                        <td>${user.lastLogin}</td>
                    </tr>
                </table>
            </div>
        </div>
    `;
}

// Filter Functions
function applyUserFilters() {
    const roleFilter = $('#filterRole').val();
    const statusFilter = $('#filterStatus').val();
    const dateFilter = $('#filterDate').val();
    const searchTerm = $('#searchInput').val();

    // Show loading state
    $('#usersTableBody').html(`
        <tr>
            <td colspan="6" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Memuat data pengguna...</p>
            </td>
        </tr>
    `);

    // Make AJAX request
    $.ajax({
        url: '/admin/users/filter',
        method: 'POST',
        data: {
            role: roleFilter,
            status: statusFilter,
            date: dateFilter,
            search: searchTerm,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#usersTableBody').html(response.html);

                // Show result message
                if (roleFilter || statusFilter || dateFilter || searchTerm) {
                    console.log(`Filter applied: ${response.count} users found`);
                }

                // Show empty state if no results
                if (response.count === 0) {
                    $('#usersTableBody').html(`
                        <tr>
                            <td colspan="6" class="text-center py-4 text-muted">
                                Tidak ada pengguna yang sesuai dengan filter yang dipilih
                            </td>
                        </tr>
                    `);
                }
            } else {
                Swal.fire('Error', response.message || 'Terjadi kesalahan saat memfilter data', 'error');
            }
        },
        error: function() {
            Swal.fire('Error', 'Terjadi kesalahan saat memfilter data', 'error');
            // Reload page on error
            location.reload();
        }
    });
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function resetUserFilters() {
    $('#filterRole').val('');
    $('#filterStatus').val('');
    $('#filterDate').val('');
    $('#searchInput').val('');

    // Apply filters with empty values to reload all data
    applyUserFilters();
}

// Document ready
$(document).ready(function() {
    // Real-time filter event listeners
    $('#filterRole, #filterStatus, #filterDate').change(function() {
        applyUserFilters();
    });

    $('#searchInput').on('input', debounce(function() {
        applyUserFilters();
    }, 500));

    $('#searchBtn').click(function() {
        applyUserFilters();
    });

    $('#searchInput').keypress(function(e) {
        if (e.which === 13) { // Enter key
            applyUserFilters();
        }
    });
});
</script>

<!-- Modal Tambah Admin -->
<div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAdminModalLabel">Tambah Admin Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addAdminForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_nama" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="admin_nama" name="nama" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="admin_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_no_hp" class="form-label">Nomor HP</label>
                        <input type="text" class="form-control" id="admin_no_hp" name="no_hp">
                    </div>
                    <div class="mb-3">
                        <label for="admin_foto_profil" class="form-label">Foto Profil</label>
                        <input type="file" class="form-control" id="admin_foto_profil" name="foto_profil" accept="image/*">
                        <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                        <div id="admin_foto_preview" class="mt-2" style="display: none;">
                            <img id="admin_preview_image" src="" alt="Preview" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="admin_password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_password_confirmation" class="form-label">Konfirmasi Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="admin_password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Simpan Admin
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle add admin form submission
$('#addAdminForm').submit(function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('role', 'admin');

    $.ajax({
        url: '/admin/users',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Admin baru berhasil ditambahkan',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
                $('#addAdminModal').modal('hide');
                $('#addAdminForm')[0].reset();
            } else {
                Swal.fire('Error', response.message || 'Terjadi kesalahan', 'error');
            }
        },
        error: function(xhr) {
            let message = 'Terjadi kesalahan saat menambah admin';
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = Object.values(xhr.responseJSON.errors).flat();
                message = errors.join('<br>');
            }
            Swal.fire('Error', message, 'error');
        }
    });
});

// Photo preview functionality for admin
$('#admin_foto_profil').change(function() {
    const file = this.files[0];
    if (file) {
        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            Swal.fire('Error', 'Ukuran file terlalu besar. Maksimal 2MB', 'error');
            $(this).val('');
            $('#admin_foto_preview').hide();
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            Swal.fire('Error', 'Format file tidak didukung. Gunakan JPG, PNG, atau GIF', 'error');
            $(this).val('');
            $('#admin_foto_preview').hide();
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#admin_preview_image').attr('src', e.target.result);
            $('#admin_foto_preview').show();
        };
        reader.readAsDataURL(file);
    } else {
        $('#admin_foto_preview').hide();
    }
});
</script>
@endpush
