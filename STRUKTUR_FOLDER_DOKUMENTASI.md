# Dokumentasi Struktur Folder Platform UMKM

## 📁 Struktur Folder yang Telah Dibuat

### 1. **Views Structure**
```
resources/views/
├── layouts/
│   ├── app.blade.php              # Layout utama
│   ├── landingpage.blade.php      # Layout untuk landing page
│   └── dashboard.blade.php        # Layout untuk dashboard
├── auth/
│   ├── login.blade.php            # Halaman login
│   ├── register.blade.php         # <PERSON>aman registrasi
│   ├── forgot-password.blade.php  # <PERSON>aman lupa password
│   └── reset-password.blade.php   # Halaman reset password
├── landingpage/
│   ├── beranda.blade.php          # Halaman beranda
│   ├── profil.blade.php           # Halaman profil platform
│   ├── berita.blade.php           # Halaman berita
│   ├── pelatihan.blade.php        # <PERSON>aman pelatihan
│   ├── galeri.blade.php           # Halaman galeri
│   ├── peta.blade.php             # Halaman peta UMKM
│   └── kontak.blade.php           # Halaman kontak
├── admin/
│   ├── dashboard.blade.php        # Dashboard admin
│   └── umkm/
│       ├── index.blade.php        # List UMKM
│       ├── show.blade.php         # Detail UMKM
│       └── edit.blade.php         # Edit UMKM
└── umkm/
    ├── dashboard.blade.php        # Dashboard UMKM
    ├── profil.blade.php           # Profil UMKM
    └── edit-profil.blade.php      # Edit profil UMKM
```

### 2. **Controllers Structure**
```
app/Http/Controllers/
├── Auth/
│   └── AuthController.php         # Handle authentication
├── Landingpage/
│   └── LandingpageController.php  # Handle landing page
├── Admin/
│   ├── AdminController.php        # Handle admin dashboard
│   └── UmkmController.php         # Handle UMKM management
├── Umkm/
│   └── UmkmDashboardController.php # Handle UMKM dashboard
└── WilayahController.php          # Handle wilayah API
```

### 3. **Assets Structure**
```
public/
├── css/
│   └── app.css                    # CSS utama aplikasi
└── js/
    ├── app.js                     # JavaScript utama
    └── wilayah-api.js             # JavaScript untuk API wilayah
```

## 🎨 CSS Classes yang Tersedia

### **Buttons**
```css
.btn                    # Base button class
.btn-primary           # Blue button
.btn-outline           # Outline button
.btn-success           # Green button
.btn-danger            # Red button
.btn-warning           # Yellow button
.btn-secondary         # Gray button
```

### **Forms**
```css
.form-group            # Form group wrapper
.form-label            # Form label
.form-input            # Text input
.form-select           # Select dropdown
.form-textarea         # Textarea
.form-checkbox         # Checkbox
.form-error            # Error state
.error-message         # Error message
```

### **Cards**
```css
.card                  # Card container
.card-header           # Card header
.card-title            # Card title
.card-body             # Card body
.card-footer           # Card footer
```

### **Layout**
```css
.header                # Header section
.nav-link              # Navigation link
.nav-link.active       # Active navigation
.hero-section          # Hero section
.section               # Content section
.section-title         # Section title
.footer                # Footer section
```

### **Dashboard**
```css
.sidebar               # Sidebar container
.sidebar-header        # Sidebar header
.sidebar-nav           # Sidebar navigation
.sidebar-link          # Sidebar link
.sidebar-link.active   # Active sidebar link
.dashboard-header      # Dashboard header
.dashboard-content     # Dashboard content
```

### **Stats & Components**
```css
.stats-grid            # Stats grid layout
.stat-card             # Stat card
.stat-icon             # Stat icon
.stat-value            # Stat value
.stat-label            # Stat label
.badge                 # Badge component
.alert                 # Alert component
```

## 🛣️ Routes Structure

### **Landing Page Routes**
```php
Route::prefix('/')->name('landingpage.')->group(function () {
    Route::get('/', [LandingpageController::class, 'beranda'])->name('beranda');
    Route::get('/profil', [LandingpageController::class, 'profil'])->name('profil');
    Route::get('/berita', [LandingpageController::class, 'berita'])->name('berita');
    // ... other routes
});
```

### **Auth Routes**
```php
Route::prefix('auth')->name('auth.')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    // ... other routes
});
```

### **Admin Routes**
```php
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    
    Route::prefix('umkm')->name('umkm.')->group(function () {
        Route::get('/', [AdminUmkmController::class, 'index'])->name('index');
        Route::get('/{id}', [AdminUmkmController::class, 'show'])->name('show');
        // ... other routes
    });
});
```

### **UMKM Routes**
```php
Route::prefix('umkm')->name('umkm.')->middleware(['auth', 'umkm'])->group(function () {
    Route::get('/dashboard', [UmkmDashboardController::class, 'dashboard'])->name('dashboard');
    Route::get('/profil', [UmkmDashboardController::class, 'profil'])->name('profil');
    // ... other routes
});
```

## 📋 Layout Usage

### **Landing Page Layout**
```blade
@extends('layouts.landingpage')

@section('title', 'Page Title')

@section('main-content')
    <!-- Your content here -->
@endsection
```

### **Dashboard Layout**
```blade
@extends('layouts.dashboard')

@section('title', 'Dashboard Title')
@section('sidebar-title', 'Dashboard Name')
@section('page-title', 'Page Title')
@section('page-subtitle', 'Page Description')

@section('sidebar-menu')
    <!-- Sidebar menu items -->
@endsection

@section('dashboard-content')
    <!-- Dashboard content -->
@endsection
```

### **Basic Layout**
```blade
@extends('layouts.app')

@section('title', 'Page Title')

@push('styles')
    <!-- Additional CSS -->
@endpush

@section('content')
    <!-- Your content -->
@endsection

@push('scripts')
    <!-- Additional JavaScript -->
@endpush
```

## 🔧 JavaScript Usage

### **Global Functions**
```javascript
// CSRF-enabled fetch
fetchWithCSRF(url, options)

// Loading states
setLoading(element, isLoading)

// Element visibility
showElement(element)
hideElement(element)
toggleElement(element)

// Form validation
showError(input, message)
clearError(input)

// Alerts
showAlert(message, type)
confirmAction(message, callback)
```

### **Wilayah API**
```javascript
// Initialize wilayah dropdowns
WilayahAPI.initializeWilayahDropdowns()

// Load specific data
WilayahAPI.loadProvinces()
WilayahAPI.loadRegencies(provinceCode, type)
WilayahAPI.loadDistricts(regencyCode, type)
WilayahAPI.loadVillages(districtCode, type)
```

## 🚀 Development Workflow

### **Adding New Pages**
1. Create controller in appropriate folder
2. Create view in appropriate folder
3. Add route with proper prefix and naming
4. Use appropriate layout
5. Add CSS classes as needed

### **Adding New Features**
1. Add JavaScript to appropriate file
2. Add CSS to app.css
3. Update layout if needed
4. Test responsiveness

### **Best Practices**
1. Use Route::prefix for grouping
2. Use named routes for easy URL generation
3. Use layouts to avoid code duplication
4. Use CSS classes instead of inline styles
5. Organize controllers by functionality
6. Use middleware for authentication/authorization

## 📝 TODO Items

### **Controllers to Implement**
- [ ] AuthController methods
- [ ] LandingpageController methods
- [ ] AdminController methods
- [ ] UmkmController methods
- [ ] UmkmDashboardController methods

### **Views to Create**
- [ ] auth/forgot-password.blade.php
- [ ] auth/reset-password.blade.php
- [ ] landingpage/profil.blade.php
- [ ] landingpage/berita.blade.php
- [ ] landingpage/pelatihan.blade.php
- [ ] landingpage/galeri.blade.php
- [ ] landingpage/peta.blade.php
- [ ] landingpage/kontak.blade.php
- [ ] admin/umkm/index.blade.php
- [ ] admin/umkm/show.blade.php
- [ ] admin/umkm/edit.blade.php
- [ ] umkm/profil.blade.php
- [ ] umkm/edit-profil.blade.php

### **Features to Add**
- [ ] Authentication middleware
- [ ] Role-based access control
- [ ] Form validation
- [ ] Database integration
- [ ] File upload handling
- [ ] Email notifications
- [ ] API documentation

## 🔗 Route Names Reference

```php
// Landing Page
route('landingpage.beranda')
route('landingpage.profil')
route('landingpage.berita')

// Auth
route('auth.login')
route('auth.register')
route('auth.logout')

// Admin
route('admin.dashboard')
route('admin.umkm.index')
route('admin.umkm.show', $id)

// UMKM
route('umkm.dashboard')
route('umkm.profil')
route('umkm.profil.edit')

// API
route('api.wilayah.provinces')
route('api.wilayah.regencies', $provinceCode)
```
