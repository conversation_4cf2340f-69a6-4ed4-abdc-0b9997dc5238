<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Berita;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Get statistics
        $totalUmkm = User::where('role', 'umkm')->count();
        $umkmVerified = 0; // Will implement when we have verification status
        $umkmPending = $totalUmkm; // For now, all are pending
        $totalBerita = Berita::count();

        // Get recent UMKM registrations
        $recentUmkms = User::with(['profil', 'usaha'])
            ->where('role', 'umkm')
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.dashboard-ltr', compact(
            'totalUmkm',
            'umkmVerified',
            'umkmPending',
            'totalBerita',
            'recentUmkms'
        ));
    }
}
