// ===== WILAYAH API JAVASCRIPT =====

// API Configuration - Using Laravel WilayahController
const WILAYAH_API = {
    base: '/api/wilayah',
    provinces: '/api/wilayah/provinces',
    regencies: '/api/wilayah/regencies',
    districts: '/api/wilayah/districts',
    villages: '/api/wilayah/villages'
};

// Loading state management for select elements
function setSelectLoading(selectElement, isLoading) {
    if (isLoading) {
        selectElement.innerHTML = '<option value="">Memuat...</option>';
        selectElement.disabled = true;
    } else {
        selectElement.disabled = false;
    }
}

// Clear dependent dropdowns
function clearDependentDropdowns(level, type) {
    const dropdowns = {
        pribadi: ['kabupaten_pribadi', 'kecamatan_pribadi', 'desa_pribadi'],
        usaha: ['kecamatan_usaha', 'desa_usaha']
    };
    
    const startIndex = level === 'provinsi' ? 1 : level === 'kabupaten' ? 2 : level === 'kecamatan' ? 3 : 4;
    
    for (let i = startIndex; i < dropdowns[type].length; i++) {
        const element = document.getElementById(dropdowns[type][i]);
        if (element) {
            const label = element.previousElementSibling?.textContent?.replace(' *', '') || 'Pilih';
            element.innerHTML = `<option value="">Pilih ${label}</option>`;
        }
    }
}

// Fetch data from Laravel WilayahController
async function fetchWilayahData(url) {
    try {
        const response = await fetchWithCSRF(url, { method: 'GET' });
        if (!response.ok) throw new Error('Network response was not ok');
        const result = await response.json();
        
        // Handle Laravel WilayahController response format
        if (result.data) {
            return result.data;
        }
        
        // Handle direct API response format (fallback)
        if (result.status === 'success' && result.data) {
            return result.data;
        }
        
        return [];
    } catch (error) {
        console.error('Error fetching wilayah data:', error);
        showAlert('Gagal memuat data wilayah. Silakan coba lagi.', 'danger');
        return [];
    }
}

// Load provinces for alamat pribadi (bebas seluruh Indonesia)
async function loadProvinces() {
    const data = await fetchWilayahData(WILAYAH_API.provinces);
    
    const select = document.getElementById('provinsi_pribadi');
    if (!select) return;
    
    select.innerHTML = '<option value="">Pilih Provinsi</option>';
    
    data.forEach(province => {
        const option = document.createElement('option');
        // Handle both formats: Laravel controller and direct API
        option.value = province.kode_wilayah || province.code;
        option.textContent = province.nama_wilayah || province.name;
        select.appendChild(option);
    });
}

// Load districts for Purworejo (33.06) - alamat usaha (lock)
async function loadPurworejoDistricts() {
    const data = await fetchWilayahData(`${WILAYAH_API.districts}/33.06`);
    
    const select = document.getElementById('kecamatan_usaha');
    if (!select) return;
    
    select.innerHTML = '<option value="">Pilih Kecamatan</option>';
    
    data.forEach(district => {
        const option = document.createElement('option');
        // Handle both formats: Laravel controller and direct API
        option.value = district.kode_wilayah || district.code;
        option.textContent = district.nama_wilayah || district.name;
        select.appendChild(option);
    });
}

// Load regencies for alamat pribadi
async function loadRegencies(provinceCode, type) {
    const selectId = `kabupaten_${type}`;
    const select = document.getElementById(selectId);
    if (!select) return;
    
    setSelectLoading(select, true);
    clearDependentDropdowns('kabupaten', type);
    
    const data = await fetchWilayahData(`${WILAYAH_API.regencies}/${provinceCode}`);
    
    select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
    data.forEach(regency => {
        const option = document.createElement('option');
        // Handle both formats: Laravel controller and direct API
        option.value = regency.kode_wilayah || regency.code;
        option.textContent = regency.nama_wilayah || regency.name;
        select.appendChild(option);
    });
    
    setSelectLoading(select, false);
}

// Load districts
async function loadDistricts(regencyCode, type) {
    const selectId = `kecamatan_${type}`;
    const select = document.getElementById(selectId);
    if (!select) return;
    
    setSelectLoading(select, true);
    clearDependentDropdowns('kecamatan', type);
    
    const data = await fetchWilayahData(`${WILAYAH_API.districts}/${regencyCode}`);
    
    select.innerHTML = '<option value="">Pilih Kecamatan</option>';
    data.forEach(district => {
        const option = document.createElement('option');
        // Handle both formats: Laravel controller and direct API
        option.value = district.kode_wilayah || district.code;
        option.textContent = district.nama_wilayah || district.name;
        select.appendChild(option);
    });
    
    setSelectLoading(select, false);
}

// Load villages
async function loadVillages(districtCode, type) {
    const selectId = `desa_${type}`;
    const select = document.getElementById(selectId);
    if (!select) return;
    
    setSelectLoading(select, true);
    
    const data = await fetchWilayahData(`${WILAYAH_API.villages}/${districtCode}`);
    
    select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    data.forEach(village => {
        const option = document.createElement('option');
        // Handle both formats: Laravel controller and direct API
        option.value = village.kode_wilayah || village.code;
        option.textContent = village.nama_wilayah || village.name;
        select.appendChild(option);
    });
    
    setSelectLoading(select, false);
}

// Initialize wilayah dropdowns
function initializeWilayahDropdowns() {
    // Load initial data
    loadProvinces(); // For alamat pribadi (bebas seluruh Indonesia)
    loadPurworejoDistricts(); // For alamat usaha (lock ke Purworejo)

    // Province change handler for alamat pribadi
    const provinsiPribadi = document.getElementById('provinsi_pribadi');
    if (provinsiPribadi) {
        provinsiPribadi.addEventListener('change', function() {
            if (this.value) {
                loadRegencies(this.value, 'pribadi');
            } else {
                clearDependentDropdowns('provinsi', 'pribadi');
            }
        });
    }

    // Regency change handler for alamat pribadi
    const kabupatenPribadi = document.getElementById('kabupaten_pribadi');
    if (kabupatenPribadi) {
        kabupatenPribadi.addEventListener('change', function() {
            if (this.value) {
                loadDistricts(this.value, 'pribadi');
            } else {
                clearDependentDropdowns('kabupaten', 'pribadi');
            }
        });
    }

    // District change handlers
    const kecamatanPribadi = document.getElementById('kecamatan_pribadi');
    if (kecamatanPribadi) {
        kecamatanPribadi.addEventListener('change', function() {
            if (this.value) {
                loadVillages(this.value, 'pribadi');
            } else {
                const desaPribadi = document.getElementById('desa_pribadi');
                if (desaPribadi) {
                    desaPribadi.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            }
        });
    }

    // District change handler for alamat usaha (lock ke Purworejo)
    const kecamatanUsaha = document.getElementById('kecamatan_usaha');
    if (kecamatanUsaha) {
        kecamatanUsaha.addEventListener('change', function() {
            if (this.value) {
                loadVillages(this.value, 'usaha');
            } else {
                const desaUsaha = document.getElementById('desa_usaha');
                if (desaUsaha) {
                    desaUsaha.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
                }
            }
        });
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeWilayahDropdowns();
});

// Export functions for manual use
window.WilayahAPI = {
    loadProvinces,
    loadPurworejoDistricts,
    loadRegencies,
    loadDistricts,
    loadVillages,
    initializeWilayahDropdowns
};
