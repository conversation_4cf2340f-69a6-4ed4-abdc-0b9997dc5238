@extends('layouts.app')

@section('title', 'Registrasi UMKM - PLUT Purworejo')

@push('styles')
<link href="{{ asset('css/register-form.css') }}" rel="stylesheet">
<style>
/* Enhanced Registration Form Styles */
.registration-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
}

.form-header {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
}

.form-header h2 {
    color: #1f2937;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #6b7280;
    font-size: 1rem;
}

.step-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    margin-right: 1rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e5e7eb;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.alert {
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.alert-info {
    background: #dbeafe;
    border: 1px solid #93c5fd;
    color: #1e40af;
}

.alert-warning {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    color: #92400e;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-outline-primary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    color: white;
}

.btn-outline-danger {
    background: transparent;
    color: #ef4444;
    border: 2px solid #ef4444;
}

.btn-outline-danger:hover {
    background: #ef4444;
    color: white;
}

.legalitas-checkbox {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.legalitas-checkbox:hover {
    border-color: #667eea;
    background: #f8faff;
}

.legalitas-checkbox input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #667eea;
}

.legalitas-form {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8faff;
    border-radius: 10px;
    border: 1px solid #e0e7ff;
    display: none;
}

.legalitas-form.active {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.validation-feedback {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.validation-feedback.valid {
    color: #10b981;
}

.validation-feedback.invalid {
    color: #ef4444;
}
</style>
@endpush

@section('content')
<div class="registration-container">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="form-card">
            <div class="form-header">
                <h2>Registrasi UMKM</h2>
                <p>Bergabunglah dengan Platform UMKM Purworejo</p>
                <div class="mt-4">
                    <span class="text-sm text-gray-600">
                        Sudah punya akun?
                        <a href="{{ route('auth.login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                            Masuk ke dashboard
                        </a>
                    </span>
                </div>
            </div>

        @if ($errors->any())
            <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4 mx-8">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Terjadi kesalahan pada form:
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <form id="registrationForm" class="p-8" action="{{ route('auth.register.store') }}" method="POST">
            @csrf
            <div class="card-body space-y-8">

                <!-- Step 1: Data Pribadi -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator active">1</span>
                        Data Pribadi
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="nama" class="form-label">Nama Lengkap *</label>
                            <input id="nama" name="nama" type="text" required class="form-input"
                                   placeholder="Masukkan nama lengkap sesuai KTP" value="{{ old('nama') }}">
                            @error('nama')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="email" class="form-label">Email *</label>
                            <input id="email" name="email" type="email" required class="form-input"
                                   placeholder="<EMAIL>" value="{{ old('email') }}">
                            <div id="email-feedback" class="text-sm mt-1"></div>
                            @error('email')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="no_hp" class="form-label">Nomor HP/WhatsApp *</label>
                            <input id="no_hp" name="no_hp" type="tel" required class="form-input"
                                   placeholder="08xxxxxxxxxx" value="{{ old('no_hp') }}">
                            @error('no_hp')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="jenis_kelamin" class="form-label">Jenis Kelamin *</label>
                            <select id="jenis_kelamin" name="jenis_kelamin" required class="form-select">
                                <option value="">Pilih Jenis Kelamin</option>
                                <option value="Laki-laki" {{ old('jenis_kelamin') == 'Laki-laki' ? 'selected' : '' }}>Laki-laki</option>
                                <option value="Perempuan" {{ old('jenis_kelamin') == 'Perempuan' ? 'selected' : '' }}>Perempuan</option>
                            </select>
                            @error('jenis_kelamin')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="nik" class="form-label">NIK (Nomor Induk Kependudukan) *</label>
                        <input id="nik" name="nik" type="text" maxlength="16" required class="form-input"
                               placeholder="16 digit NIK" value="{{ old('nik') }}">
                        <div id="nik-feedback" class="text-sm mt-1"></div>
                        @error('nik')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <!-- Step 2: Alamat Pribadi -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator">2</span>
                        Alamat Pribadi
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="provinsi" class="form-label">Provinsi *</label>
                            <select id="provinsi" name="provinsi" required class="form-select">
                                <option value="">Pilih Provinsi</option>
                            </select>
                            @error('provinsi')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="kabupaten" class="form-label">Kabupaten/Kota *</label>
                            <select id="kabupaten" name="kabupaten" required class="form-select">
                                <option value="">Pilih Kabupaten/Kota</option>
                            </select>
                            @error('kabupaten')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="kecamatan" class="form-label">Kecamatan *</label>
                            <select id="kecamatan" name="kecamatan" required class="form-select">
                                <option value="">Pilih Kecamatan</option>
                            </select>
                            @error('kecamatan')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="desa" class="form-label">Desa/Kelurahan *</label>
                            <select id="desa" name="desa" required class="form-select">
                                <option value="">Pilih Desa/Kelurahan</option>
                            </select>
                            @error('desa')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="alamat_lengkap" class="form-label">Alamat Lengkap *</label>
                        <textarea id="alamat_lengkap" name="alamat_lengkap" rows="3" required class="form-textarea"
                                  placeholder="Jalan, RT/RW, No. Rumah, dan detail alamat lainnya">{{ old('alamat_lengkap') }}</textarea>
                        @error('alamat_lengkap')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <!-- Step 3: Data Usaha -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator">3</span>
                        Data Usaha
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="nama_usaha" class="form-label">Nama Usaha *</label>
                            <input id="nama_usaha" name="nama_usaha" type="text" required class="form-input"
                                   placeholder="Nama usaha/perusahaan" value="{{ old('nama_usaha') }}">
                            @error('nama_usaha')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="nama_merk" class="form-label">Nama Merk/Brand</label>
                            <input id="nama_merk" name="nama_merk" type="text" class="form-input"
                                   placeholder="Nama brand/merk produk (jika ada)" value="{{ old('nama_merk') }}">
                            @error('nama_merk')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="bidang_usaha" class="form-label">Bidang Usaha *</label>
                        <select id="bidang_usaha" name="bidang_usaha" required class="form-select">
                            <option value="">Pilih bidang usaha</option>
                            <option value="makanan_minuman" {{ old('bidang_usaha') == 'makanan_minuman' ? 'selected' : '' }}>Makanan & Minuman</option>
                            <option value="kerajinan_tangan" {{ old('bidang_usaha') == 'kerajinan_tangan' ? 'selected' : '' }}>Kerajinan Tangan</option>
                            <option value="perdagangan" {{ old('bidang_usaha') == 'perdagangan' ? 'selected' : '' }}>Perdagangan</option>
                            <option value="jasa" {{ old('bidang_usaha') == 'jasa' ? 'selected' : '' }}>Jasa</option>
                        </select>
                        @error('bidang_usaha')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Media Sosial (Opsional)</label>
                        <p class="text-sm text-gray-600 mb-3">Pilih platform media sosial yang Anda gunakan untuk usaha:</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Instagram -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('instagram')">
                                <input type="checkbox" id="instagram_checkbox" name="media_sosial_types[]" value="Instagram">
                                <label for="instagram_checkbox" class="font-medium">Instagram</label>
                            </div>
                            <div id="instagram_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="instagram_username" class="form-label">Username Instagram</label>
                                    <input id="instagram_username" name="instagram_username" type="text" class="form-input"
                                           placeholder="@username_anda">
                                </div>
                            </div>

                            <!-- Facebook -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('facebook')">
                                <input type="checkbox" id="facebook_checkbox" name="media_sosial_types[]" value="Facebook">
                                <label for="facebook_checkbox" class="font-medium">Facebook</label>
                            </div>
                            <div id="facebook_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="facebook_page" class="form-label">Halaman Facebook</label>
                                    <input id="facebook_page" name="facebook_page" type="text" class="form-input"
                                           placeholder="Nama halaman Facebook">
                                </div>
                            </div>

                            <!-- WhatsApp Business -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('whatsapp')">
                                <input type="checkbox" id="whatsapp_checkbox" name="media_sosial_types[]" value="WhatsApp">
                                <label for="whatsapp_checkbox" class="font-medium">WhatsApp Business</label>
                            </div>
                            <div id="whatsapp_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="whatsapp_number" class="form-label">Nomor WhatsApp Business</label>
                                    <input id="whatsapp_number" name="whatsapp_number" type="text" class="form-input"
                                           placeholder="08xxxxxxxxxx">
                                </div>
                            </div>

                            <!-- TikTok -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('tiktok')">
                                <input type="checkbox" id="tiktok_checkbox" name="media_sosial_types[]" value="TikTok">
                                <label for="tiktok_checkbox" class="font-medium">TikTok</label>
                            </div>
                            <div id="tiktok_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="tiktok_username" class="form-label">Username TikTok</label>
                                    <input id="tiktok_username" name="tiktok_username" type="text" class="form-input"
                                           placeholder="@username_anda">
                                </div>
                            </div>

                            <!-- YouTube -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('youtube')">
                                <input type="checkbox" id="youtube_checkbox" name="media_sosial_types[]" value="YouTube">
                                <label for="youtube_checkbox" class="font-medium">YouTube</label>
                            </div>
                            <div id="youtube_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="youtube_channel" class="form-label">Channel YouTube</label>
                                    <input id="youtube_channel" name="youtube_channel" type="text" class="form-input"
                                           placeholder="Nama channel YouTube">
                                </div>
                            </div>

                            <!-- Website -->
                            <div class="legalitas-checkbox" onclick="toggleMediaSosial('website')">
                                <input type="checkbox" id="website_checkbox" name="media_sosial_types[]" value="Website">
                                <label for="website_checkbox" class="font-medium">Website</label>
                            </div>
                            <div id="website_form" class="legalitas-form">
                                <div class="form-group">
                                    <label for="website_url" class="form-label">URL Website</label>
                                    <input id="website_url" name="website_url" type="url" class="form-input"
                                           placeholder="https://www.website-anda.com">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="deskripsi" class="form-label">Deskripsi Usaha</label>
                        <textarea id="deskripsi" name="deskripsi" rows="3" class="form-textarea"
                                  placeholder="Jelaskan secara singkat tentang usaha Anda">{{ old('deskripsi') }}</textarea>
                        @error('deskripsi')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <!-- Step 4: Alamat Usaha -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator">4</span>
                        Alamat Usaha
                    </h3>

                    <div class="alert alert-info mb-4">
                        <i class="bi bi-info-circle me-2"></i>
                        Alamat usaha harus berada di wilayah Kabupaten Purworejo, Jawa Tengah
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label">Provinsi</label>
                            <input type="text" value="Jawa Tengah" readonly class="form-input bg-gray-100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Kabupaten</label>
                            <input type="text" value="Purworejo" readonly class="form-input bg-gray-100">
                        </div>
                        <div class="form-group">
                            <label for="kecamatan_usaha" class="form-label">Kecamatan Usaha *</label>
                            <select id="kecamatan_usaha" name="kecamatan_usaha" required class="form-select">
                                <option value="">Pilih Kecamatan</option>
                            </select>
                            @error('kecamatan_usaha')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="desa_usaha" class="form-label">Desa/Kelurahan Usaha *</label>
                            <select id="desa_usaha" name="desa_usaha" required class="form-select">
                                <option value="">Pilih Desa/Kelurahan</option>
                            </select>
                            @error('desa_usaha')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="alamat_lengkap_usaha" class="form-label">Alamat Lengkap Usaha *</label>
                        <textarea id="alamat_lengkap_usaha" name="alamat_lengkap_usaha" rows="3" required class="form-textarea"
                                  placeholder="Jalan, RT/RW, No. Rumah/Toko, dan detail alamat usaha">{{ old('alamat_lengkap_usaha') }}</textarea>
                        @error('alamat_lengkap_usaha')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <!-- Step 5: Legalitas (Optional) -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator">5</span>
                        Legalitas Usaha (Opsional)
                    </h3>

                    <div class="alert alert-warning mb-4">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Pilih dokumen legalitas yang sudah Anda miliki (opsional).
                    </div>

                    <div id="legalitas-container">
                        <!-- NIB -->
                        <div class="legalitas-checkbox" onclick="toggleLegalitas('nib')">
                            <input type="checkbox" id="nib_checkbox" name="legalitas_types[]" value="NIB">
                            <label for="nib_checkbox" class="font-medium">NIB (Nomor Induk Berusaha)</label>
                        </div>
                        <div id="nib_form" class="legalitas-form">
                            <div class="form-group">
                                <label for="nib_nomor" class="form-label">Nomor NIB</label>
                                <input id="nib_nomor" name="nib_nomor" type="text" class="form-input"
                                       placeholder="Masukkan nomor NIB">
                            </div>
                        </div>

                        <!-- SIUP -->
                        <div class="legalitas-checkbox" onclick="toggleLegalitas('siup')">
                            <input type="checkbox" id="siup_checkbox" name="legalitas_types[]" value="SIUP">
                            <label for="siup_checkbox" class="font-medium">SIUP (Surat Izin Usaha Perdagangan)</label>
                        </div>
                        <div id="siup_form" class="legalitas-form">
                            <div class="form-group">
                                <label for="siup_nomor" class="form-label">Nomor SIUP</label>
                                <input id="siup_nomor" name="siup_nomor" type="text" class="form-input"
                                       placeholder="Masukkan nomor SIUP">
                            </div>
                        </div>

                        <!-- NPWP -->
                        <div class="legalitas-checkbox" onclick="toggleLegalitas('npwp')">
                            <input type="checkbox" id="npwp_checkbox" name="legalitas_types[]" value="NPWP">
                            <label for="npwp_checkbox" class="font-medium">NPWP (Nomor Pokok Wajib Pajak)</label>
                        </div>
                        <div id="npwp_form" class="legalitas-form">
                            <div class="form-group">
                                <label for="npwp_nomor" class="form-label">Nomor NPWP</label>
                                <input id="npwp_nomor" name="npwp_nomor" type="text" class="form-input"
                                       placeholder="Masukkan nomor NPWP">
                            </div>
                        </div>

                        <!-- TDP -->
                        <div class="legalitas-checkbox" onclick="toggleLegalitas('tdp')">
                            <input type="checkbox" id="tdp_checkbox" name="legalitas_types[]" value="TDP">
                            <label for="tdp_checkbox" class="font-medium">TDP (Tanda Daftar Perusahaan)</label>
                        </div>
                        <div id="tdp_form" class="legalitas-form">
                            <div class="form-group">
                                <label for="tdp_nomor" class="form-label">Nomor TDP</label>
                                <input id="tdp_nomor" name="tdp_nomor" type="text" class="form-input"
                                       placeholder="Masukkan nomor TDP">
                            </div>
                        </div>

                        <!-- Lainnya -->
                        <div class="legalitas-checkbox" onclick="toggleLegalitas('lainnya')">
                            <input type="checkbox" id="lainnya_checkbox" name="legalitas_types[]" value="Lainnya">
                            <label for="lainnya_checkbox" class="font-medium">Lainnya</label>
                        </div>
                        <div id="lainnya_form" class="legalitas-form">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label for="lainnya_nama" class="form-label">Nama Dokumen</label>
                                    <input id="lainnya_nama" name="lainnya_nama" type="text" class="form-input"
                                           placeholder="Nama dokumen legalitas">
                                </div>
                                <div class="form-group">
                                    <label for="lainnya_nomor" class="form-label">Nomor Dokumen</label>
                                    <input id="lainnya_nomor" name="lainnya_nomor" type="text" class="form-input"
                                           placeholder="Nomor dokumen legalitas">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 6: Password -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span class="step-indicator">6</span>
                        Keamanan Akun
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="password" class="form-label">Password *</label>
                            <input id="password" name="password" type="password" required class="form-input"
                                   placeholder="Minimal 8 karakter">
                            @error('password')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">Konfirmasi Password *</label>
                            <input id="password_confirmation" name="password_confirmation" type="password" required class="form-input"
                                   placeholder="Ulangi password">
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-section">
                    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center">
                        <div class="text-sm text-gray-600">
                            Sudah punya akun?
                            <a href="{{ route('auth.login') }}" class="font-medium text-blue-600 hover:text-blue-500">Masuk ke Dashboard</a>
                        </div>
                        <button type="submit" class="btn btn-primary w-full sm:w-auto">
                            <i class="bi bi-check-circle me-2"></i>
                            Daftar Sekarang
                        </button>
                    </div>
                </div>
            </div>
        </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // API Wilayah Indonesia - menggunakan API dari .env
    const API_BASE = '/api/wilayah';

    // Load Provinsi
    loadProvinsi();

    // Load Kecamatan Purworejo untuk alamat usaha
    loadKecamatanPurworejo();

    async function loadProvinsi() {
        try {
            const response = await fetch(`${API_BASE}/provinces`);
            const data = await response.json();
            const provinces = data.data || data;

            const provinsiSelect = document.getElementById('provinsi');
            provinsiSelect.innerHTML = '<option value="">Pilih Provinsi</option>';

            provinces.forEach(province => {
                const option = document.createElement('option');
                option.value = province.name;
                option.textContent = province.name;
                option.dataset.code = province.code;
                provinsiSelect.appendChild(option);
            });

            // Set default to Jawa Tengah if available
            const jawaTengah = Array.from(provinsiSelect.options).find(option =>
                option.textContent.toLowerCase().includes('jawa tengah')
            );
            if (jawaTengah) {
                jawaTengah.selected = true;
                loadKabupaten(jawaTengah.dataset.code);
            }
        } catch (error) {
            console.error('Error loading provinces:', error);
        }
    }

    async function loadKabupaten(provinceCode) {
        try {
            const response = await fetch(`${API_BASE}/regencies/${provinceCode}`);
            const data = await response.json();
            const regencies = data.data || data;

            const kabupatenSelect = document.getElementById('kabupaten');
            kabupatenSelect.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';

            regencies.forEach(regency => {
                const option = document.createElement('option');
                option.value = regency.name;
                option.textContent = regency.name;
                option.dataset.code = regency.code;
                kabupatenSelect.appendChild(option);
            });

            // Set default to Purworejo if available
            const purworejo = Array.from(kabupatenSelect.options).find(option =>
                option.textContent.toLowerCase().includes('purworejo')
            );
            if (purworejo) {
                purworejo.selected = true;
                loadKecamatan(purworejo.dataset.code);
            }
        } catch (error) {
            console.error('Error loading regencies:', error);
        }
    }

    async function loadKecamatan(regencyCode) {
        try {
            const response = await fetch(`${API_BASE}/districts/${regencyCode}`);
            const data = await response.json();
            const districts = data.data || data;

            const kecamatanSelect = document.getElementById('kecamatan');
            kecamatanSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';

            districts.forEach(district => {
                const option = document.createElement('option');
                option.value = district.name;
                option.textContent = district.name;
                option.dataset.code = district.code;
                kecamatanSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading districts:', error);
        }
    }

    async function loadDesa(districtCode, targetSelectId) {
        try {
            const response = await fetch(`${API_BASE}/villages/${districtCode}`);
            const data = await response.json();
            const villages = data.data || data;

            const desaSelect = document.getElementById(targetSelectId);
            desaSelect.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';

            villages.forEach(village => {
                const option = document.createElement('option');
                option.value = village.name;
                option.textContent = village.name;
                desaSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading villages:', error);
        }
    }

    async function loadKecamatanPurworejo() {
        // Load kecamatan untuk alamat usaha (khusus Purworejo)
        try {
            // Kode Purworejo: 33.06
            const response = await fetch(`${API_BASE}/districts/33.06`);
            const data = await response.json();
            const districts = data.data || data;

            const kecamatanUsahaSelect = document.getElementById('kecamatan_usaha');
            kecamatanUsahaSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';

            districts.forEach(district => {
                const option = document.createElement('option');
                option.value = district.name;
                option.textContent = district.name;
                option.dataset.code = district.code;
                kecamatanUsahaSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading Purworejo districts:', error);
            // Fallback manual data
            const kecamatanUsahaSelect = document.getElementById('kecamatan_usaha');
            const kecamatanPurworejo = [
                'Bagelen', 'Banyuurip', 'Bayan', 'Bener', 'Bruno', 'Gebang',
                'Grabag', 'Kaligesing', 'Kemiri', 'Kutoarjo', 'Loano', 'Ngombol',
                'Pituruh', 'Purwodadi', 'Purworejo', 'Rembang'
            ];

            kecamatanUsahaSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
            kecamatanPurworejo.forEach((kecamatan, index) => {
                const option = document.createElement('option');
                option.value = kecamatan;
                option.textContent = kecamatan;
                option.dataset.code = `33.06.${String(index + 1).padStart(2, '0')}`;
                kecamatanUsahaSelect.appendChild(option);
            });
        }
    }

    // Event Listeners
    document.getElementById('provinsi').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.code) {
            loadKabupaten(selectedOption.dataset.code);
        }
        document.getElementById('kabupaten').innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
        document.getElementById('kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
        document.getElementById('desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    });

    document.getElementById('kabupaten').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.code) {
            loadKecamatan(selectedOption.dataset.code);
        }
        document.getElementById('kecamatan').innerHTML = '<option value="">Pilih Kecamatan</option>';
        document.getElementById('desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    });

    document.getElementById('kecamatan').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.code) {
            loadDesa(selectedOption.dataset.code, 'desa');
        }
        document.getElementById('desa').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    });

    document.getElementById('kecamatan_usaha').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.code) {
            loadDesa(selectedOption.dataset.code, 'desa_usaha');
        }
        document.getElementById('desa_usaha').innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
    });

    // NIK Validation
    const nikInput = document.getElementById('nik');
    const nikFeedback = document.getElementById('nik-feedback');

    nikInput.addEventListener('blur', function() {
        const nik = this.value;
        if (nik.length === 16) {
            checkNik(nik);
        }
    });

    function checkNik(nik) {
        fetch('{{ route("auth.check-nik") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ nik: nik })
        })
        .then(response => response.json())
        .then(data => {
            if (data.available) {
                nikFeedback.innerHTML = '<span class="text-green-500">✓ ' + data.message + '</span>';
            } else {
                nikFeedback.innerHTML = '<span class="text-red-500">✗ ' + data.message + '</span>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // Email Validation
    const emailInput = document.getElementById('email');
    const emailFeedback = document.getElementById('email-feedback');

    emailInput.addEventListener('blur', function() {
        const email = this.value;
        if (email && email.includes('@')) {
            checkEmail(email);
        }
    });

    function checkEmail(email) {
        fetch('{{ route("auth.check-email") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.available) {
                emailFeedback.innerHTML = '<span class="text-green-500">✓ ' + data.message + '</span>';
            } else {
                emailFeedback.innerHTML = '<span class="text-red-500">✗ ' + data.message + '</span>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // NIK Input Validation (only numbers)
    nikInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length > 16) {
            this.value = this.value.slice(0, 16);
        }
    });

    // Phone number validation
    const phoneInput = document.getElementById('no_hp');
    phoneInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
    });

    // Legalitas Checkbox functionality
    window.toggleLegalitas = function(type) {
        const checkbox = document.getElementById(`${type}_checkbox`);
        const form = document.getElementById(`${type}_form`);

        if (checkbox.checked) {
            form.classList.add('active');
        } else {
            form.classList.remove('active');
            // Clear form inputs when unchecked
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => input.value = '');
        }
    };

    // Media Sosial Checkbox functionality
    window.toggleMediaSosial = function(type) {
        const checkbox = document.getElementById(`${type}_checkbox`);
        const form = document.getElementById(`${type}_form`);

        if (checkbox.checked) {
            form.classList.add('active');
        } else {
            form.classList.remove('active');
            // Clear form inputs when unchecked
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => input.value = '');
        }
    };

    // Form submission validation
    const form = document.getElementById('registrationForm');
    form.addEventListener('submit', function(e) {
        console.log('Form submission started');

        const nikValue = nikInput.value;
        const emailValue = emailInput.value;

        console.log('NIK:', nikValue, 'Email:', emailValue);

        if (nikValue.length !== 16) {
            e.preventDefault();
            alert('NIK harus terdiri dari 16 digit');
            nikInput.focus();
            return false;
        }

        if (!emailValue.includes('@')) {
            e.preventDefault();
            alert('Format email tidak valid');
            emailInput.focus();
            return false;
        }

        // Check if NIK or email shows error
        if (nikFeedback.innerHTML.includes('text-red-500') || emailFeedback.innerHTML.includes('text-red-500')) {
            e.preventDefault();
            alert('Silakan perbaiki error pada NIK atau email sebelum melanjutkan');
            return false;
        }

        console.log('Form validation passed, submitting...');

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Memproses...';
        }
    });
});
</script>
@endpush
