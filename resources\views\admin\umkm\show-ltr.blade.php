@extends('layouts.admin-ltr')

@section('title', 'Detail UMKM')

@push('styles')
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="/admin/dashboard-new"><i class="bx bx-home-alt"></i></a></li>
                <li class="breadcrumb-item"><a href="/admin/umkm">Kelola UMKM</a></li>
                <li class="breadcrumb-item active" aria-current="page">Detail UMKM</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            @if(isset($umkm) && $umkm->verification_status === 'pending')
                <button type="button" class="btn btn-success" onclick="approveUmkm({{ $umkm->id ?? 1 }})">
                    <i class="bi bi-check-circle"></i> Setujui
                </button>
                <button type="button" class="btn btn-danger" onclick="rejectUmkm({{ $umkm->id ?? 1 }})">
                    <i class="bi bi-x-circle"></i> Tolak
                </button>
            @elseif(isset($umkm) && $umkm->verification_status === 'verified')
                <span class="badge bg-success fs-6 me-2">
                    <i class="bi bi-check-circle"></i> Sudah Disetujui
                </span>
            @elseif(isset($umkm) && $umkm->verification_status === 'rejected')
                <span class="badge bg-danger fs-6 me-2">
                    <i class="bi bi-x-circle"></i> Ditolak
                </span>
            @endif
            <a href="{{ route('admin.umkm.edit', $umkm->id ?? 1) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <a href="{{ route('admin.umkm.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>
<!--end breadcrumb-->

<div class="row">
    <div class="col-12 col-lg-8">
        <!-- Basic Info -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <div class="d-flex align-items-center">
                    <div class="">
                        <h5 class="mb-0">Informasi Dasar</h5>
                    </div>
                    <div class="ms-auto">
                        @php
                            $statusColors = [
                                'verified' => ['bg-light-success', 'text-success', 'Terverifikasi'],
                                'pending' => ['bg-light-warning', 'text-warning', 'Menunggu Verifikasi'],
                                'rejected' => ['bg-light-danger', 'text-danger', 'Ditolak'],
                            ];
                            $status = $umkm->verification_status ?? 'pending';
                            $statusConfig = $statusColors[$status] ?? $statusColors['pending'];
                        @endphp
                        <span class="badge {{ $statusConfig[0] }} {{ $statusConfig[1] }}">
                            @if($status === 'verified')
                                <i class="bi bi-check-circle"></i>
                            @elseif($status === 'pending')
                                <i class="bi bi-clock"></i>
                            @else
                                <i class="bi bi-x-circle"></i>
                            @endif
                            {{ $statusConfig[2] }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Nama UMKM</label>
                        <p class="mb-0 fw-bold">{{ $umkm->usaha->nama_usaha ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Bidang Usaha</label>
                        <p class="mb-0">
                            @php
                                $bidangColors = [
                                    'makanan_minuman' => 'info',
                                    'kerajinan_tangan' => 'success',
                                    'perdagangan' => 'warning',
                                    'jasa' => 'danger',
                                ];
                                $bidangColor = $bidangColors[$umkm->usaha->bidang_usaha ?? ''] ?? 'secondary';
                            @endphp
                            <span class="badge bg-light-{{ $bidangColor }} text-{{ $bidangColor }}">
                                {{ ucwords(str_replace('_', ' & ', $umkm->usaha->bidang_usaha ?? 'Belum diisi')) }}
                            </span>
                        </p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Nama Merk</label>
                        <p class="mb-0">{{ $umkm->usaha->nama_merk ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Jumlah Karyawan</label>
                        <p class="mb-0">5 orang</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label text-muted">Deskripsi</label>
                        <p class="mb-0">{{ $umkm->usaha->deskripsi ?? 'Belum ada deskripsi' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verification Section -->
        @if($umkm->verification_status === 'pending')
        <div class="card radius-10 mb-4 border-warning">
            <div class="card-header bg-light-warning">
                <div class="d-flex align-items-center">
                    <div class="">
                        <h5 class="mb-0 text-warning">
                            <i class="bi bi-exclamation-triangle"></i> Menunggu Verifikasi
                        </h5>
                        <small class="text-muted">UMKM ini memerlukan verifikasi admin</small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <p class="mb-3">Silakan tinjau informasi UMKM di bawah ini dan lakukan verifikasi:</p>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" onclick="approveUmkm({{ $umkm->id }})">
                                <i class="bi bi-check-circle"></i> Setujui Verifikasi
                            </button>
                            <button type="button" class="btn btn-danger" onclick="rejectUmkm({{ $umkm->id }})">
                                <i class="bi bi-x-circle"></i> Tolak Verifikasi
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @elseif($umkm->verification_status === 'verified')
        <div class="card radius-10 mb-4 border-success">
            <div class="card-header bg-light-success">
                <div class="d-flex align-items-center">
                    <div class="">
                        <h5 class="mb-0 text-success">
                            <i class="bi bi-check-circle"></i> Sudah Terverifikasi
                        </h5>
                        <small class="text-muted">
                            Diverifikasi pada: {{ $umkm->verified_at ? $umkm->verified_at->format('d M Y H:i') : '-' }}
                        </small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-success mb-0">
                    <i class="bi bi-info-circle"></i>
                    UMKM ini telah diverifikasi dan dapat mengakses semua fitur platform.
                    @if($umkm->verification_notes)
                        <br><strong>Catatan:</strong> {{ $umkm->verification_notes }}
                    @endif
                </div>
            </div>
        </div>
        @elseif($umkm->verification_status === 'rejected')
        <div class="card radius-10 mb-4 border-danger">
            <div class="card-header bg-light-danger">
                <div class="d-flex align-items-center">
                    <div class="">
                        <h5 class="mb-0 text-danger">
                            <i class="bi bi-x-circle"></i> Verifikasi Ditolak
                        </h5>
                        <small class="text-muted">
                            Ditolak pada: {{ $umkm->verified_at ? $umkm->verified_at->format('d M Y H:i') : '-' }}
                        </small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-danger mb-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    Verifikasi UMKM ini telah ditolak.
                    @if($umkm->verification_notes)
                        <br><strong>Alasan:</strong> {{ $umkm->verification_notes }}
                    @endif
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-success" onclick="approveUmkm({{ $umkm->id }})">
                        <i class="bi bi-check-circle"></i> Setujui Verifikasi
                    </button>
                </div>
            </div>
        </div>
        @endif

        <!-- Contact Info -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Informasi Kontak</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Nama Pemilik</label>
                        <p class="mb-0">{{ $umkm->nama ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Email</label>
                        <p class="mb-0">{{ $umkm->email ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Nomor Telepon</label>
                        <p class="mb-0">{{ $umkm->no_hp ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">NIK</label>
                        <p class="mb-0">{{ $umkm->profil->nik ?? 'Belum diisi' }}</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label text-muted">Alamat</label>
                        <p class="mb-0">{{ $umkm->profil->alamat_lengkap ?? 'Belum diisi' }}</p>
                        @if($umkm->profil)
                        <small class="text-muted">
                            {{ $umkm->profil->desa }}, {{ $umkm->profil->kecamatan }}, {{ $umkm->profil->kabupaten }}, {{ $umkm->profil->provinsi }}
                        </small>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Info -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Informasi Usaha</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Modal Awal</label>
                        <p class="mb-0 fw-bold text-success">Rp 50.000.000</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Omzet Bulanan</label>
                        <p class="mb-0 fw-bold text-primary">Rp 25.000.000</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Aset</label>
                        <p class="mb-0 fw-bold text-info">Rp 75.000.000</p>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label text-muted">Status Tempat Usaha</label>
                        <p class="mb-0">Milik Sendiri</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents -->
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Dokumen</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex align-items-center">
                        <div class="me-3">
                            <i class="bi bi-file-earmark-pdf-fill text-danger fs-4"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">KTP Pemilik</h6>
                            <p class="mb-0 text-muted small">ktp-budi-santoso.pdf</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye"></i> Lihat
                            </button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex align-items-center">
                        <div class="me-3">
                            <i class="bi bi-file-earmark-pdf-fill text-danger fs-4"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">SIUP</h6>
                            <p class="mb-0 text-muted small">siup-warung-makan.pdf</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye"></i> Lihat
                            </button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex align-items-center">
                        <div class="me-3">
                            <i class="bi bi-image-fill text-success fs-4"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">Foto Usaha</h6>
                            <p class="mb-0 text-muted small">foto-warung.jpg</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye"></i> Lihat
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-12 col-lg-4">
        <!-- Profile Photo -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Foto Profil</h5>
            </div>
            <div class="card-body text-center">
                <div class="mx-auto mb-3" style="width: 120px; height: 120px;">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center h-100 fs-1 fw-bold">
                        WM
                    </div>
                </div>
                <h6 class="mb-1">Warung Makan Sederhana</h6>
                <p class="text-muted small mb-3">Budi Santoso</p>
                <button class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-camera"></i> Ganti Foto
                </button>
            </div>
        </div>

        <!-- Status -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted small">Status Verifikasi</label>
                    <div>
                        <span class="badge bg-light-success text-success">
                            <i class="bi bi-check-circle"></i> Terverifikasi
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted small">Tanggal Daftar</label>
                    <p class="mb-0">15 Januari 2024</p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted small">Terakhir Update</label>
                    <p class="mb-0">20 Juli 2024</p>
                </div>
                <div>
                    <label class="form-label text-muted small">Diverifikasi oleh</label>
                    <p class="mb-0">Admin PLUT</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card radius-10 mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Aksi Cepat</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="sendNotification()">
                        <i class="bi bi-bell"></i> Kirim Notifikasi
                    </button>
                    <button class="btn btn-success" onclick="inviteToTraining()">
                        <i class="bi bi-book"></i> Undang ke Pelatihan
                    </button>
                    <button class="btn btn-warning" onclick="markAsFeatured()">
                        <i class="bi bi-star"></i> Tandai Unggulan
                    </button>
                    <button class="btn btn-danger" onclick="deactivateUmkm()">
                        <i class="bi bi-x-circle"></i> Nonaktifkan
                    </button>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="card radius-10">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">Log Aktivitas</h5>
            </div>
            <div class="card-body">
                <div class="timeline-wrapper">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1 small">UMKM diverifikasi</h6>
                            <p class="mb-0 text-muted small">2 hari yang lalu</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1 small">Profil diperbarui</h6>
                            <p class="mb-0 text-muted small">1 minggu yang lalu</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1 small">Mendaftar</h6>
                            <p class="mb-0 text-muted small">6 bulan yang lalu</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // CSRF Token Setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    function approveUmkm(id) {
        Swal.fire({
            title: 'Setujui UMKM?',
            text: "UMKM akan disetujui dan statusnya berubah menjadi verified.",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, setujui!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/umkm/${id}/approve`,
                    method: 'POST',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error', 'Terjadi kesalahan saat menyetujui UMKM', 'error');
                    }
                });
            }
        });
    }

    function rejectUmkm(id) {
        Swal.fire({
            title: 'Tolak UMKM?',
            html: `
                <p>UMKM akan ditolak dan statusnya berubah menjadi rejected.</p>
                <div class="mt-3">
                    <label for="rejection-reason" class="form-label">Alasan Penolakan:</label>
                    <textarea id="rejection-reason" class="form-control" rows="3" placeholder="Masukkan alasan penolakan..."></textarea>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, tolak!',
            cancelButtonText: 'Batal',
            preConfirm: () => {
                const reason = document.getElementById('rejection-reason').value;
                if (!reason.trim()) {
                    Swal.showValidationMessage('Alasan penolakan harus diisi');
                    return false;
                }
                return reason;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/umkm/${id}/reject`,
                    method: 'POST',
                    data: {
                        reason: result.value
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error', 'Terjadi kesalahan saat menolak UMKM', 'error');
                    }
                });
            }
        });
    }

function sendNotification() {
    // Open modal or redirect to notification page
    console.log('Sending notification');
}

function inviteToTraining() {
    // Open modal or redirect to training invitation page
    console.log('Inviting to training');
}

function markAsFeatured() {
    if (confirm('Tandai UMKM ini sebagai unggulan?')) {
        // Ajax call to mark as featured
        console.log('Marking as featured');
    }
}

function deactivateUmkm() {
    if (confirm('Nonaktifkan UMKM ini?')) {
        // Ajax call to deactivate
        console.log('Deactivating UMKM');
    }
}
</script>

<style>
.timeline-wrapper {
    position: relative;
}
.timeline-item {
    display: flex;
    margin-bottom: 1rem;
    position: relative;
}
.timeline-marker {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}
.timeline-content {
    flex: 1;
}
.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 18px;
    width: 2px;
    height: calc(100% - 0.25rem);
    background-color: #e9ecef;
}
</style>
@endpush
